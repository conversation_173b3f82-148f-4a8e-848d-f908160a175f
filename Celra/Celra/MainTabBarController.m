//
//  MainTabBarController.m
//  Celra
//
//  Main tab bar controller implementation
//

#import "MainTabBarController.h"
#import "CelraDesignSystem.h"
#import "HallwayViewController.h"
#import "ProfileViewController.h"

@interface MainTabBarController ()

@end

@implementation MainTabBarController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupTabBar];
    [self setupViewControllers];
}

- (void)setupTabBar {
    // Set tab bar appearance
    self.tabBar.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
    self.tabBar.tintColor = [CelraDesignSystem accentColor];
    self.tabBar.unselectedItemTintColor = [CelraDesignSystem secondaryBackgroundColor];
    
    // iOS 13+ appearance customization
    if (@available(iOS 13.0, *)) {
        UITabBarAppearance *appearance = [[UITabBarAppearance alloc] init];
        [appearance configureWithOpaqueBackground];
        appearance.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
        appearance.selectionIndicatorTintColor = [CelraDesignSystem accentColor];
        
        // Normal state
        appearance.stackedLayoutAppearance.normal.iconColor = [CelraDesignSystem secondaryBackgroundColor];
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = @{
            NSForegroundColorAttributeName: [CelraDesignSystem secondaryBackgroundColor],
            NSFontAttributeName: [CelraDesignSystem captionFont]
        };
        
        // Selected state
        appearance.stackedLayoutAppearance.selected.iconColor = [CelraDesignSystem accentColor];
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = @{
            NSForegroundColorAttributeName: [CelraDesignSystem accentColor],
            NSFontAttributeName: [CelraDesignSystem captionFont]
        };
        
        self.tabBar.standardAppearance = appearance;
        if (@available(iOS 15.0, *)) {
            self.tabBar.scrollEdgeAppearance = appearance;
        }
    } else {
        // iOS 12 fallback
        [[UITabBarItem appearance] setTitleTextAttributes:@{
            NSForegroundColorAttributeName: [CelraDesignSystem secondaryBackgroundColor],
            NSFontAttributeName: [CelraDesignSystem captionFont]
        } forState:UIControlStateNormal];
        
        [[UITabBarItem appearance] setTitleTextAttributes:@{
            NSForegroundColorAttributeName: [CelraDesignSystem accentColor],
            NSFontAttributeName: [CelraDesignSystem captionFont]
        } forState:UIControlStateSelected];
    }
}

- (void)setupViewControllers {
    // Hallway (AI Characters) Tab
    HallwayViewController *hallwayVC = [[HallwayViewController alloc] init];
    UINavigationController *hallwayNav = [[UINavigationController alloc] initWithRootViewController:hallwayVC];
    hallwayNav.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"Advisors" 
                                                          image:[self tabBarImageNamed:@"door.fill"] 
                                                            tag:0];
    
    // Profile (My Page) Tab
    ProfileViewController *profileVC = [[ProfileViewController alloc] init];
    UINavigationController *profileNav = [[UINavigationController alloc] initWithRootViewController:profileVC];
    profileNav.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"My Desk" 
                                                          image:[self tabBarImageNamed:@"person.fill"] 
                                                            tag:1];
    
    // Set view controllers
    self.viewControllers = @[hallwayNav, profileNav];
    
    // Customize navigation bars
    [self customizeNavigationBar:hallwayNav.navigationBar];
    [self customizeNavigationBar:profileNav.navigationBar];
}

- (UIImage *)tabBarImageNamed:(NSString *)imageName {
    // For iOS 12 compatibility, use system images if available, otherwise use custom images
    if (@available(iOS 13.0, *)) {
        return [UIImage systemImageNamed:imageName];
    } else {
        // Fallback to custom images for iOS 12
        return [UIImage imageNamed:imageName];
    }
}

- (void)customizeNavigationBar:(UINavigationBar *)navigationBar {
    navigationBar.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
    navigationBar.tintColor = [CelraDesignSystem accentColor];
    
    if (@available(iOS 13.0, *)) {
        UINavigationBarAppearance *appearance = [[UINavigationBarAppearance alloc] init];
        [appearance configureWithOpaqueBackground];
        appearance.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
        appearance.titleTextAttributes = @{
            NSForegroundColorAttributeName: [CelraDesignSystem textPrimaryColor],
            NSFontAttributeName: [CelraDesignSystem headlineFont]
        };
        appearance.largeTitleTextAttributes = @{
            NSForegroundColorAttributeName: [CelraDesignSystem textPrimaryColor],
            NSFontAttributeName: [CelraDesignSystem titleFont]
        };
        
        navigationBar.standardAppearance = appearance;
        navigationBar.compactAppearance = appearance;
        navigationBar.scrollEdgeAppearance = appearance;
    } else {
        // iOS 12 fallback
        navigationBar.barTintColor = [CelraDesignSystem primaryBackgroundColor];
        navigationBar.titleTextAttributes = @{
            NSForegroundColorAttributeName: [CelraDesignSystem textPrimaryColor],
            NSFontAttributeName: [CelraDesignSystem headlineFont]
        };
        if (@available(iOS 11.0, *)) {
            navigationBar.largeTitleTextAttributes = @{
                NSForegroundColorAttributeName: [CelraDesignSystem textPrimaryColor],
                NSFontAttributeName: [CelraDesignSystem titleFont]
            };
        }
    }
}

@end
