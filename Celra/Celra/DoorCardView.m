//
//  DoorCardView.m
//  Celra
//
//  Door card view implementation
//

#import "DoorCardView.h"
#import "CelraDesignSystem.h"

@interface DoorCardView ()

@property (nonatomic, strong) AICharacter *character;
@property (nonatomic, strong) UIView *doorView;
@property (nonatomic, strong) UIView *doorFrameView;
@property (nonatomic, strong) UIView *doorHandleView;
@property (nonatomic, strong) UIView *peepholeView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *englishNameLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;

@end

@implementation DoorCardView

- (instancetype)initWithCharacter:(AICharacter *)character {
    self = [super init];
    if (self) {
        _character = character;
        [self setupUI];
        [self setupConstraints];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    self.layer.cornerRadius = [CelraDesignSystem cornerRadiusL];
    self.layer.shadowColor = [CelraDesignSystem shadowColor].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 8);
    self.layer.shadowRadius = 16;
    self.layer.shadowOpacity = 1.0;
    
    // Door frame view
    self.doorFrameView = [[UIView alloc] init];
    self.doorFrameView.backgroundColor = [UIColor colorWithRed:0.4 green:0.3 blue:0.2 alpha:1.0]; // Wood color
    self.doorFrameView.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.doorFrameView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.doorFrameView];
    
    // Door view
    self.doorView = [[UIView alloc] init];
    self.doorView.backgroundColor = self.character.doorColor;
    self.doorView.layer.cornerRadius = [CelraDesignSystem cornerRadiusS];
    self.doorView.layer.borderWidth = 2;
    self.doorView.layer.borderColor = [UIColor colorWithRed:0.3 green:0.2 blue:0.1 alpha:1.0].CGColor;
    self.doorView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorFrameView addSubview:self.doorView];
    
    // Door handle
    self.doorHandleView = [[UIView alloc] init];
    self.doorHandleView.backgroundColor = [UIColor colorWithRed:0.8 green:0.7 blue:0.3 alpha:1.0]; // Brass color
    self.doorHandleView.layer.cornerRadius = 6;
    self.doorHandleView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorView addSubview:self.doorHandleView];
    
    // Peephole
    self.peepholeView = [[UIView alloc] init];
    self.peepholeView.backgroundColor = [UIColor colorWithRed:0.1 green:0.1 blue:0.1 alpha:1.0];
    self.peepholeView.layer.cornerRadius = 8;
    self.peepholeView.layer.borderWidth = 2;
    self.peepholeView.layer.borderColor = [UIColor colorWithRed:0.6 green:0.5 blue:0.2 alpha:1.0].CGColor;
    self.peepholeView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.doorView addSubview:self.peepholeView];
    
    // Add AI indicator in peephole
    UIView *aiIndicator = [[UIView alloc] init];
    aiIndicator.backgroundColor = self.character.accentColor;
    aiIndicator.layer.cornerRadius = 3;
    aiIndicator.translatesAutoresizingMaskIntoConstraints = NO;
    [self.peepholeView addSubview:aiIndicator];
    
    [NSLayoutConstraint activateConstraints:@[
        [aiIndicator.centerXAnchor constraintEqualToAnchor:self.peepholeView.centerXAnchor],
        [aiIndicator.centerYAnchor constraintEqualToAnchor:self.peepholeView.centerYAnchor],
        [aiIndicator.widthAnchor constraintEqualToConstant:6],
        [aiIndicator.heightAnchor constraintEqualToConstant:6]
    ]];
    
    // Character name label
    self.nameLabel = [[UILabel alloc] init];
    self.nameLabel.text = self.character.chineseName;
    self.nameLabel.font = [CelraDesignSystem headlineFont];
    self.nameLabel.textColor = [CelraDesignSystem textPrimaryColor];
    self.nameLabel.textAlignment = NSTextAlignmentCenter;
    self.nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.nameLabel];
    
    // English name label
    self.englishNameLabel = [[UILabel alloc] init];
    self.englishNameLabel.text = self.character.englishName;
    self.englishNameLabel.font = [CelraDesignSystem bodyFont];
    self.englishNameLabel.textColor = self.character.accentColor;
    self.englishNameLabel.textAlignment = NSTextAlignmentCenter;
    self.englishNameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.englishNameLabel];
    
    // Description label
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.text = self.character.shortDescription;
    self.descriptionLabel.font = [CelraDesignSystem captionFont];
    self.descriptionLabel.textColor = [CelraDesignSystem secondaryBackgroundColor];
    self.descriptionLabel.textAlignment = NSTextAlignmentCenter;
    self.descriptionLabel.numberOfLines = 2;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.descriptionLabel];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Door frame
        [self.doorFrameView.topAnchor constraintEqualToAnchor:self.topAnchor 
                                                     constant:[CelraDesignSystem spacingM]],
        [self.doorFrameView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.doorFrameView.widthAnchor constraintEqualToConstant:200],
        [self.doorFrameView.heightAnchor constraintEqualToConstant:240],
        
        // Door
        [self.doorView.topAnchor constraintEqualToAnchor:self.doorFrameView.topAnchor 
                                                constant:[CelraDesignSystem spacingS]],
        [self.doorView.leadingAnchor constraintEqualToAnchor:self.doorFrameView.leadingAnchor 
                                                    constant:[CelraDesignSystem spacingS]],
        [self.doorView.trailingAnchor constraintEqualToAnchor:self.doorFrameView.trailingAnchor 
                                                     constant:-[CelraDesignSystem spacingS]],
        [self.doorView.bottomAnchor constraintEqualToAnchor:self.doorFrameView.bottomAnchor 
                                                   constant:-[CelraDesignSystem spacingS]],
        
        // Door handle
        [self.doorHandleView.trailingAnchor constraintEqualToAnchor:self.doorView.trailingAnchor 
                                                           constant:-[CelraDesignSystem spacingM]],
        [self.doorHandleView.centerYAnchor constraintEqualToAnchor:self.doorView.centerYAnchor],
        [self.doorHandleView.widthAnchor constraintEqualToConstant:12],
        [self.doorHandleView.heightAnchor constraintEqualToConstant:24],
        
        // Peephole
        [self.peepholeView.centerXAnchor constraintEqualToAnchor:self.doorView.centerXAnchor],
        [self.peepholeView.topAnchor constraintEqualToAnchor:self.doorView.topAnchor 
                                                    constant:[CelraDesignSystem spacingXL]],
        [self.peepholeView.widthAnchor constraintEqualToConstant:16],
        [self.peepholeView.heightAnchor constraintEqualToConstant:16],
        
        // Name label
        [self.nameLabel.topAnchor constraintEqualToAnchor:self.doorFrameView.bottomAnchor 
                                                 constant:[CelraDesignSystem spacingM]],
        [self.nameLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor 
                                                     constant:[CelraDesignSystem spacingS]],
        [self.nameLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor 
                                                      constant:-[CelraDesignSystem spacingS]],
        
        // English name label
        [self.englishNameLabel.topAnchor constraintEqualToAnchor:self.nameLabel.bottomAnchor 
                                                        constant:[CelraDesignSystem spacingXS]],
        [self.englishNameLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor 
                                                            constant:[CelraDesignSystem spacingS]],
        [self.englishNameLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor 
                                                             constant:-[CelraDesignSystem spacingS]],
        
        // Description label
        [self.descriptionLabel.topAnchor constraintEqualToAnchor:self.englishNameLabel.bottomAnchor 
                                                        constant:[CelraDesignSystem spacingS]],
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor 
                                                            constant:[CelraDesignSystem spacingS]],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor 
                                                             constant:-[CelraDesignSystem spacingS]],
        [self.descriptionLabel.bottomAnchor constraintLessThanOrEqualToAnchor:self.bottomAnchor 
                                                                     constant:-[CelraDesignSystem spacingM]]
    ]];
}

@end
