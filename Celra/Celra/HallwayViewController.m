//
//  HallwayViewController.m
//  Celra
//
//  Hallway view controller implementation
//

#import "HallwayViewController.h"
#import "CelraDesignSystem.h"
#import "AICharacter.h"
#import "DoorCardView.h"
#import "ChatViewController.h"
#import "SoundManager.h"

@interface HallwayViewController () <UIScrollViewDelegate>

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) NSArray<AICharacter *> *characters;
@property (nonatomic, strong) NSMutableArray<DoorCardView *> *doorCards;
@property (nonatomic, assign) NSInteger currentIndex;
@property (nonatomic, strong) UIButton *knockButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;

@end

@implementation HallwayViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupData];
    [self setupUI];
    [self setupConstraints];
}

- (void)setupData {
    self.characters = [AICharacter allCharacters];
    self.doorCards = [NSMutableArray array];
    self.currentIndex = 0;
}

- (void)setupUI {
    self.view.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
    self.navigationItem.title = @"AI Storyboard Advisors";
    
    // Title label
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"Choose Your Advisor";
    self.titleLabel.font = [CelraDesignSystem titleFont];
    self.titleLabel.textColor = [CelraDesignSystem textPrimaryColor];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.titleLabel];
    
    // Subtitle label
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.text = @"Swipe to explore different expertise";
    self.subtitleLabel.font = [CelraDesignSystem bodyFont];
    self.subtitleLabel.textColor = [CelraDesignSystem secondaryBackgroundColor];
    self.subtitleLabel.textAlignment = NSTextAlignmentCenter;
    self.subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.subtitleLabel];
    
    // Scroll view for door cards
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.delegate = self;
    self.scrollView.pagingEnabled = YES;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.scrollView];
    
    // Create door cards
    [self createDoorCards];
    
    // Knock button
    self.knockButton = [[UIButton alloc] init];
    [self.knockButton setTitle:@"Knock" forState:UIControlStateNormal];
    [self.knockButton setTitleColor:[CelraDesignSystem primaryBackgroundColor] forState:UIControlStateNormal];
    self.knockButton.titleLabel.font = [CelraDesignSystem buttonFont];
    self.knockButton.backgroundColor = [CelraDesignSystem accentColor];
    self.knockButton.layer.cornerRadius = [CelraDesignSystem knockButtonSize] / 2;
    self.knockButton.layer.shadowColor = [CelraDesignSystem shadowColor].CGColor;
    self.knockButton.layer.shadowOffset = CGSizeMake(0, 4);
    self.knockButton.layer.shadowRadius = 8;
    self.knockButton.layer.shadowOpacity = 1.0;
    self.knockButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.knockButton addTarget:self action:@selector(knockButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.knockButton];
    
    [self updateCurrentCharacterInfo];
}

- (void)createDoorCards {
    CGFloat cardWidth = [CelraDesignSystem doorCardWidth];
    CGFloat spacing = [CelraDesignSystem spacingM];
    
    for (NSInteger i = 0; i < self.characters.count; i++) {
        AICharacter *character = self.characters[i];
        DoorCardView *doorCard = [[DoorCardView alloc] initWithCharacter:character];
        doorCard.translatesAutoresizingMaskIntoConstraints = NO;
        [self.scrollView addSubview:doorCard];
        [self.doorCards addObject:doorCard];
        
        // Position the door card
        [NSLayoutConstraint activateConstraints:@[
            [doorCard.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor 
                                                   constant:i * (cardWidth + spacing) + spacing],
            [doorCard.centerYAnchor constraintEqualToAnchor:self.scrollView.centerYAnchor],
            [doorCard.widthAnchor constraintEqualToConstant:cardWidth],
            [doorCard.heightAnchor constraintEqualToConstant:[CelraDesignSystem doorCardHeight]]
        ]];
    }
    
    // Set scroll view content size
    CGFloat contentWidth = self.characters.count * (cardWidth + spacing) + spacing;
    self.scrollView.contentSize = CGSizeMake(contentWidth, 0);
}

- (void)setupConstraints {
    CGFloat safeAreaTop = 0;
    if (@available(iOS 11.0, *)) {
        safeAreaTop = self.view.safeAreaInsets.top;
    }
    
    [NSLayoutConstraint activateConstraints:@[
        // Title label
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor 
                                                  constant:[CelraDesignSystem spacingL]],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor 
                                                      constant:[CelraDesignSystem spacingM]],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor 
                                                       constant:-[CelraDesignSystem spacingM]],
        
        // Subtitle label
        [self.subtitleLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor 
                                                     constant:[CelraDesignSystem spacingS]],
        [self.subtitleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor 
                                                         constant:[CelraDesignSystem spacingM]],
        [self.subtitleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor 
                                                          constant:-[CelraDesignSystem spacingM]],
        
        // Scroll view
        [self.scrollView.topAnchor constraintEqualToAnchor:self.subtitleLabel.bottomAnchor 
                                                  constant:[CelraDesignSystem spacingXL]],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.heightAnchor constraintEqualToConstant:[CelraDesignSystem doorCardHeight]],
        
        // Knock button
        [self.knockButton.topAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor 
                                                   constant:[CelraDesignSystem spacingXL]],
        [self.knockButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.knockButton.widthAnchor constraintEqualToConstant:[CelraDesignSystem knockButtonSize]],
        [self.knockButton.heightAnchor constraintEqualToConstant:[CelraDesignSystem knockButtonSize]]
    ]];
}

- (void)updateCurrentCharacterInfo {
    // Update any additional UI elements based on current character
    // AICharacter *currentCharacter = self.characters[self.currentIndex];
}

- (void)knockButtonTapped:(UIButton *)sender {
    AICharacter *selectedCharacter = self.characters[self.currentIndex];

    // Play knock sound effect
    [[SoundManager sharedManager] playKnockSound];

    // Add knock animation and sound effect
    [self animateKnockButton];

    // Delay navigation to allow animation to complete
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // Play door open sound
        [[SoundManager sharedManager] playDoorOpenSound];

        // Navigate to chat view controller
        ChatViewController *chatVC = [[ChatViewController alloc] initWithCharacter:selectedCharacter];
        [self.navigationController pushViewController:chatVC animated:YES];
    });
}

- (void)animateKnockButton {
    // Scale animation for knock effect
    [UIView animateWithDuration:[CelraDesignSystem animationDurationFast] 
                          delay:0 
                        options:UIViewAnimationOptionCurveEaseInOut 
                     animations:^{
        self.knockButton.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:[CelraDesignSystem animationDurationFast] 
                              delay:0 
                            options:UIViewAnimationOptionCurveEaseInOut 
                         animations:^{
            self.knockButton.transform = CGAffineTransformIdentity;
        } completion:nil];
    }];
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    CGFloat cardWidth = [CelraDesignSystem doorCardWidth];
    CGFloat spacing = [CelraDesignSystem spacingM];
    CGFloat pageWidth = cardWidth + spacing;
    
    NSInteger newIndex = (NSInteger)(scrollView.contentOffset.x / pageWidth);
    if (newIndex != self.currentIndex && newIndex >= 0 && newIndex < self.characters.count) {
        self.currentIndex = newIndex;
        [self updateCurrentCharacterInfo];
    }
}

@end
