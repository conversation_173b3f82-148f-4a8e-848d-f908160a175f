//
//  ProfileViewController.m
//  Celra
//
//  Profile view controller implementation
//

#import "ProfileViewController.h"
#import "CelraDesignSystem.h"
#import "VoiceNoteView.h"
#import "VoiceNote.h"
#import "VoiceNoteCell.h"

@interface ProfileViewController () <UITableViewDataSource, UITableViewDelegate, VoiceNoteViewDelegate>

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;
@property (nonatomic, strong) VoiceNoteView *voiceNoteView;
@property (nonatomic, strong) UITableView *notesTableView;
@property (nonatomic, strong) NSMutableArray<VoiceNote *> *voiceNotes;
@property (nonatomic, strong) UIView *deskBackgroundView;

@end

@implementation ProfileViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupData];
    [self setupUI];
    [self setupConstraints];
}

- (void)setupData {
    self.voiceNotes = [NSMutableArray array];
    
    // Load saved voice notes (in a real app, this would be from Core Data or similar)
    [self loadSavedNotes];
}

- (void)loadSavedNotes {
    // Placeholder for loading saved notes
    // In a real implementation, this would load from persistent storage
}

- (void)setupUI {
    self.view.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
    self.navigationItem.title = @"My Desk";
    
    // Scroll view
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.scrollView];
    
    // Content view
    self.contentView = [[UIView alloc] init];
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.scrollView addSubview:self.contentView];
    
    // Desk background view
    self.deskBackgroundView = [[UIView alloc] init];
    self.deskBackgroundView.backgroundColor = [UIColor colorWithRed:0.4 green:0.3 blue:0.2 alpha:1.0]; // Wood color
    self.deskBackgroundView.layer.cornerRadius = [CelraDesignSystem cornerRadiusL];
    self.deskBackgroundView.layer.shadowColor = [CelraDesignSystem shadowColor].CGColor;
    self.deskBackgroundView.layer.shadowOffset = CGSizeMake(0, 4);
    self.deskBackgroundView.layer.shadowRadius = 12;
    self.deskBackgroundView.layer.shadowOpacity = 1.0;
    self.deskBackgroundView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.deskBackgroundView];
    
    // Title label
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"My Creative Desk";
    self.titleLabel.font = [CelraDesignSystem titleFont];
    self.titleLabel.textColor = [CelraDesignSystem textPrimaryColor];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.titleLabel];
    
    // Subtitle label
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.text = @"Capture your storyboard ideas with voice notes";
    self.subtitleLabel.font = [CelraDesignSystem bodyFont];
    self.subtitleLabel.textColor = [CelraDesignSystem secondaryBackgroundColor];
    self.subtitleLabel.textAlignment = NSTextAlignmentCenter;
    self.subtitleLabel.numberOfLines = 2;
    self.subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.subtitleLabel];
    
    // Voice note view
    self.voiceNoteView = [[VoiceNoteView alloc] init];
    self.voiceNoteView.delegate = self;
    self.voiceNoteView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.deskBackgroundView addSubview:self.voiceNoteView];
    
    // Notes table view
    self.notesTableView = [[UITableView alloc] init];
    self.notesTableView.dataSource = self;
    self.notesTableView.delegate = self;
    self.notesTableView.backgroundColor = [UIColor clearColor];
    self.notesTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.notesTableView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.notesTableView registerClass:[VoiceNoteCell class] forCellReuseIdentifier:@"VoiceNoteCell"];
    [self.contentView addSubview:self.notesTableView];
    
    // Add desk items for visual appeal
    [self addDeskItems];
}

- (void)addDeskItems {
    // Coffee cup
    UIView *coffeeCup = [[UIView alloc] init];
    coffeeCup.backgroundColor = [UIColor colorWithRed:0.8 green:0.6 blue:0.4 alpha:1.0];
    coffeeCup.layer.cornerRadius = 15;
    coffeeCup.translatesAutoresizingMaskIntoConstraints = NO;
    [self.deskBackgroundView addSubview:coffeeCup];
    
    // Pencil holder
    UIView *pencilHolder = [[UIView alloc] init];
    pencilHolder.backgroundColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    pencilHolder.layer.cornerRadius = 8;
    pencilHolder.translatesAutoresizingMaskIntoConstraints = NO;
    [self.deskBackgroundView addSubview:pencilHolder];
    
    // Position desk items
    [NSLayoutConstraint activateConstraints:@[
        // Coffee cup
        [coffeeCup.topAnchor constraintEqualToAnchor:self.deskBackgroundView.topAnchor 
                                            constant:[CelraDesignSystem spacingM]],
        [coffeeCup.trailingAnchor constraintEqualToAnchor:self.deskBackgroundView.trailingAnchor 
                                                 constant:-[CelraDesignSystem spacingM]],
        [coffeeCup.widthAnchor constraintEqualToConstant:30],
        [coffeeCup.heightAnchor constraintEqualToConstant:30],
        
        // Pencil holder
        [pencilHolder.topAnchor constraintEqualToAnchor:self.deskBackgroundView.topAnchor 
                                               constant:[CelraDesignSystem spacingM]],
        [pencilHolder.leadingAnchor constraintEqualToAnchor:self.deskBackgroundView.leadingAnchor 
                                                   constant:[CelraDesignSystem spacingM]],
        [pencilHolder.widthAnchor constraintEqualToConstant:20],
        [pencilHolder.heightAnchor constraintEqualToConstant:40]
    ]];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Scroll view
        [self.scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.scrollView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor],
        
        // Content view
        [self.contentView.topAnchor constraintEqualToAnchor:self.scrollView.topAnchor],
        [self.contentView.leadingAnchor constraintEqualToAnchor:self.scrollView.leadingAnchor],
        [self.contentView.trailingAnchor constraintEqualToAnchor:self.scrollView.trailingAnchor],
        [self.contentView.bottomAnchor constraintEqualToAnchor:self.scrollView.bottomAnchor],
        [self.contentView.widthAnchor constraintEqualToAnchor:self.scrollView.widthAnchor],
        
        // Title label
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.contentView.topAnchor 
                                                  constant:[CelraDesignSystem spacingL]],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor 
                                                      constant:[CelraDesignSystem spacingM]],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor 
                                                       constant:-[CelraDesignSystem spacingM]],
        
        // Subtitle label
        [self.subtitleLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor 
                                                     constant:[CelraDesignSystem spacingS]],
        [self.subtitleLabel.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor 
                                                         constant:[CelraDesignSystem spacingM]],
        [self.subtitleLabel.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor 
                                                          constant:-[CelraDesignSystem spacingM]],
        
        // Desk background
        [self.deskBackgroundView.topAnchor constraintEqualToAnchor:self.subtitleLabel.bottomAnchor 
                                                          constant:[CelraDesignSystem spacingL]],
        [self.deskBackgroundView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor 
                                                              constant:[CelraDesignSystem spacingM]],
        [self.deskBackgroundView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor 
                                                               constant:-[CelraDesignSystem spacingM]],
        [self.deskBackgroundView.heightAnchor constraintEqualToConstant:200],
        
        // Voice note view
        [self.voiceNoteView.centerXAnchor constraintEqualToAnchor:self.deskBackgroundView.centerXAnchor],
        [self.voiceNoteView.centerYAnchor constraintEqualToAnchor:self.deskBackgroundView.centerYAnchor],
        [self.voiceNoteView.widthAnchor constraintEqualToConstant:150],
        [self.voiceNoteView.heightAnchor constraintEqualToConstant:100],
        
        // Notes table view
        [self.notesTableView.topAnchor constraintEqualToAnchor:self.deskBackgroundView.bottomAnchor 
                                                      constant:[CelraDesignSystem spacingL]],
        [self.notesTableView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor],
        [self.notesTableView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor],
        [self.notesTableView.heightAnchor constraintEqualToConstant:400],
        [self.notesTableView.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor 
                                                         constant:-[CelraDesignSystem spacingL]]
    ]];
}

- (void)addVoiceNote:(VoiceNote *)note {
    [self.voiceNotes insertObject:note atIndex:0];
    [self.notesTableView reloadData];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.voiceNotes.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    VoiceNoteCell *cell = [tableView dequeueReusableCellWithIdentifier:@"VoiceNoteCell" forIndexPath:indexPath];
    VoiceNote *note = self.voiceNotes[indexPath.row];
    [cell configureWithVoiceNote:note];
    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 80;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    // Handle note selection (e.g., edit note)
}

#pragma mark - VoiceNoteViewDelegate

- (void)voiceNoteView:(UIView *)view didCreateNote:(VoiceNote *)note {
    [self addVoiceNote:note];
}

@end
