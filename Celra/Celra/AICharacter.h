//
//  AICharacter.h
//  Celra
//
//  AI Character model for storyboard advisors
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, AICharacterType) {
    AICharacterTypeRhythmGuide = 0,
    AICharacterTypeActionFrame,
    AICharacterTypeDialoguePanel,
    AICharacterTypeEmotionFrame,
    AICharacterTypeComposePro,
    AICharacterTypeBeginnerGuide,
    AICharacterTypeSciFiFrame,
    AICharacterTypeComedyFrame,
    AICharacterTypeDigitalToolPro,
    AICharacterTypeRevisionPro
};

@interface AICharacter : NSObject

@property (nonatomic, assign) AICharacterType type;
@property (nonatomic, strong) NSString *chineseName;
@property (nonatomic, strong) NSString *englishName;
@property (nonatomic, strong) NSString *shortDescription;
@property (nonatomic, strong) NSString *longDescription;
@property (nonatomic, strong) UIColor *doorColor;
@property (nonatomic, strong) UIColor *accentColor;
@property (nonatomic, strong) NSString *doorImageName;
@property (nonatomic, strong) NSString *avatarImageName;

+ (instancetype)characterWithType:(AICharacterType)type;
+ (NSArray<AICharacter *> *)allCharacters;
+ (AICharacter *)characterAtIndex:(NSInteger)index;

@end

NS_ASSUME_NONNULL_END
