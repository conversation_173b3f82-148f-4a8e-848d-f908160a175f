//
//  CelraDesignSystem.m
//  Celra
//
//  Design system implementation
//

#import "CelraDesignSystem.h"

@implementation CelraDesignSystem

// MARK: - Colors
+ (UIColor *)primaryBackgroundColor {
    return [UIColor colorWithRed:0.102 green:0.102 blue:0.180 alpha:1.0]; // #1A1A2E
}

+ (UIColor *)secondaryBackgroundColor {
    return [UIColor colorWithRed:0.918 green:0.918 blue:0.937 alpha:1.0]; // #EAEAEF
}

+ (UIColor *)accentColor {
    return [UIColor colorWithRed:1.0 green:0.843 blue:0.0 alpha:1.0]; // #FFD700
}

+ (UIColor *)textPrimaryColor {
    return [UIColor whiteColor];
}

+ (UIColor *)textSecondaryColor {
    return [UIColor colorWithRed:0.2 green:0.2 blue:0.2 alpha:1.0];
}

+ (UIColor *)cardBackgroundColor {
    return [UIColor colorWithRed:0.15 green:0.15 blue:0.25 alpha:0.9];
}

+ (UIColor *)shadowColor {
    return [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.3];
}

// MARK: - Typography
+ (UIFont *)titleFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:28 weight:UIFontWeightBold];
    } else {
        return [UIFont boldSystemFontOfSize:28];
    }
}

+ (UIFont *)headlineFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:20 weight:UIFontWeightSemibold];
    } else {
        return [UIFont boldSystemFontOfSize:20];
    }
}

+ (UIFont *)bodyFont {
    return [UIFont systemFontOfSize:16 weight:UIFontWeightRegular];
}

+ (UIFont *)captionFont {
    return [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
}

+ (UIFont *)buttonFont {
    if (@available(iOS 13.0, *)) {
        return [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    } else {
        return [UIFont boldSystemFontOfSize:16];
    }
}

// MARK: - Spacing
+ (CGFloat)spacingXS { return 4.0; }
+ (CGFloat)spacingS { return 8.0; }
+ (CGFloat)spacingM { return 16.0; }
+ (CGFloat)spacingL { return 24.0; }
+ (CGFloat)spacingXL { return 32.0; }
+ (CGFloat)spacingXXL { return 48.0; }

// MARK: - Corner Radius
+ (CGFloat)cornerRadiusS { return 8.0; }
+ (CGFloat)cornerRadiusM { return 12.0; }
+ (CGFloat)cornerRadiusL { return 16.0; }
+ (CGFloat)cornerRadiusXL { return 24.0; }

// MARK: - Shadows
+ (NSShadow *)cardShadow {
    NSShadow *shadow = [[NSShadow alloc] init];
    shadow.shadowColor = [self shadowColor];
    shadow.shadowOffset = CGSizeMake(0, 4);
    shadow.shadowBlurRadius = 12;
    return shadow;
}

+ (NSShadow *)buttonShadow {
    NSShadow *shadow = [[NSShadow alloc] init];
    shadow.shadowColor = [self shadowColor];
    shadow.shadowOffset = CGSizeMake(0, 2);
    shadow.shadowBlurRadius = 8;
    return shadow;
}

// MARK: - Animation Durations
+ (NSTimeInterval)animationDurationFast { return 0.2; }
+ (NSTimeInterval)animationDurationNormal { return 0.3; }
+ (NSTimeInterval)animationDurationSlow { return 0.5; }

// MARK: - Layout Constants
+ (CGFloat)tabBarHeight { return 83.0; } // Standard tab bar height + safe area
+ (CGFloat)navigationBarHeight { return 44.0; }
+ (CGFloat)doorCardWidth { return 280.0; }
+ (CGFloat)doorCardHeight { return 400.0; }
+ (CGFloat)knockButtonSize { return 60.0; }

@end
