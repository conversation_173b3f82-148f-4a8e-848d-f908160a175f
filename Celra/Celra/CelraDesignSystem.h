//
//  CelraDesignSystem.h
//  Celra
//
//  Design system constants and utilities
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface CelraDesignSystem : NSObject

// MARK: - Colors
+ (UIColor *)primaryBackgroundColor;      // #1A1A2E - Deep blue-gray
+ (UIColor *)secondaryBackgroundColor;    // #EAEAEF - Light gray
+ (UIColor *)accentColor;                 // #FFD700 - Gold
+ (UIColor *)textPrimaryColor;            // White for dark backgrounds
+ (UIColor *)textSecondaryColor;          // Dark gray for light backgrounds
+ (UIColor *)cardBackgroundColor;         // Semi-transparent overlay
+ (UIColor *)shadowColor;                 // For depth effects

// MARK: - Typography
+ (UIFont *)titleFont;                    // Large titles
+ (UIFont *)headlineFont;                 // Section headers
+ (UIFont *)bodyFont;                     // Regular text
+ (UIFont *)captionFont;                  // Small text
+ (UIFont *)buttonFont;                   // Button text

// MARK: - Spacing
+ (CGFloat)spacingXS;                     // 4pt
+ (CGFloat)spacingS;                      // 8pt
+ (CGFloat)spacingM;                      // 16pt
+ (CGFloat)spacingL;                      // 24pt
+ (CGFloat)spacingXL;                     // 32pt
+ (CGFloat)spacingXXL;                    // 48pt

// MARK: - Corner Radius
+ (CGFloat)cornerRadiusS;                 // 8pt
+ (CGFloat)cornerRadiusM;                 // 12pt
+ (CGFloat)cornerRadiusL;                 // 16pt
+ (CGFloat)cornerRadiusXL;                // 24pt

// MARK: - Shadows
+ (NSShadow *)cardShadow;
+ (NSShadow *)buttonShadow;

// MARK: - Animation Durations
+ (NSTimeInterval)animationDurationFast;   // 0.2s
+ (NSTimeInterval)animationDurationNormal; // 0.3s
+ (NSTimeInterval)animationDurationSlow;   // 0.5s

// MARK: - Layout Constants
+ (CGFloat)tabBarHeight;
+ (CGFloat)navigationBarHeight;
+ (CGFloat)doorCardWidth;
+ (CGFloat)doorCardHeight;
+ (CGFloat)knockButtonSize;

@end

NS_ASSUME_NONNULL_END
