//
//  ViewController.m
//  Celra
//
//  Created by asde007 on 2025/8/4.
//

#import "ViewController.h"
#import "MainTabBarController.h"
#import "CelraDesignSystem.h"

@interface ViewController ()

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupMainInterface];
}

- (void)setupMainInterface {
    // Create and present the main tab bar controller
    MainTabBarController *mainTabBarController = [[MainTabBarController alloc] init];

    // Set as root view controller
    self.view.window.rootViewController = mainTabBarController;

    // Alternative: Present modally if needed
    // [self presentViewController:mainTabBarController animated:YES completion:nil];
}

@end
