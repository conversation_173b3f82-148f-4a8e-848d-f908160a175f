//
//  AppDelegate.m
//  Celra
//
//  Created by asde007 on 2025/8/4.
//

#import "AppDelegate.h"

// Design System Colors
#define CELRA_PRIMARY_BG [UIColor colorWithRed:0.102 green:0.102 blue:0.180 alpha:1.0]
#define CELRA_ACCENT [UIColor colorWithRed:1.0 green:0.843 blue:0.0 alpha:1.0]
#define CELRA_SECONDARY [UIColor colorWithRed:0.918 green:0.918 blue:0.937 alpha:1.0]
#define CELRA_CARD_BG [UIColor colorWithRed:0.15 green:0.15 blue:0.25 alpha:0.9]

@interface CelraHallwayViewController : UIViewController
@end

@interface CelraProfileViewController : UIViewController
@end

@interface AppDelegate ()

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // Create window for all iOS versions
    self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];

    // Create the main tab bar controller with beautiful UI
    UITabBarController *tabBarController = [[UITabBarController alloc] init];

    // Create hallway view controller
    CelraHallwayViewController *hallwayVC = [[CelraHallwayViewController alloc] init];
    UINavigationController *hallwayNav = [[UINavigationController alloc] initWithRootViewController:hallwayVC];
    hallwayNav.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"Advisors" image:nil tag:0];

    // Create profile view controller
    CelraProfileViewController *profileVC = [[CelraProfileViewController alloc] init];
    UINavigationController *profileNav = [[UINavigationController alloc] initWithRootViewController:profileVC];
    profileNav.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"My Desk" image:nil tag:1];

    // Set view controllers
    tabBarController.viewControllers = @[hallwayNav, profileNav];

    // Customize tab bar appearance
    tabBarController.tabBar.backgroundColor = CELRA_PRIMARY_BG;
    tabBarController.tabBar.tintColor = CELRA_ACCENT;
    tabBarController.tabBar.unselectedItemTintColor = CELRA_SECONDARY;

    // Customize navigation bars
    [self customizeNavigationBar:hallwayNav.navigationBar];
    [self customizeNavigationBar:profileNav.navigationBar];

    self.window.rootViewController = tabBarController;
    [self.window makeKeyAndVisible];

    return YES;
}

- (void)customizeNavigationBar:(UINavigationBar *)navigationBar {
    navigationBar.backgroundColor = CELRA_PRIMARY_BG;
    navigationBar.tintColor = CELRA_ACCENT;
    navigationBar.barTintColor = CELRA_PRIMARY_BG;
    navigationBar.titleTextAttributes = @{
        NSForegroundColorAttributeName: [UIColor whiteColor],
        NSFontAttributeName: [UIFont boldSystemFontOfSize:20]
    };
}

@end

// MARK: - Hallway View Controller Implementation
@implementation CelraHallwayViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    self.view.backgroundColor = CELRA_PRIMARY_BG;
    self.navigationItem.title = @"AI Storyboard Advisors";

    // Create scroll view for door cards
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.backgroundColor = [UIColor clearColor];
    scrollView.showsHorizontalScrollIndicator = NO;
    scrollView.pagingEnabled = YES;
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:scrollView];

    // Title label
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Choose Your Advisor";
    titleLabel.font = [UIFont boldSystemFontOfSize:28];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:titleLabel];

    // Subtitle label
    UILabel *subtitleLabel = [[UILabel alloc] init];
    subtitleLabel.text = @"Swipe to explore different expertise";
    subtitleLabel.font = [UIFont systemFontOfSize:16];
    subtitleLabel.textColor = CELRA_SECONDARY;
    subtitleLabel.textAlignment = NSTextAlignmentCenter;
    subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:subtitleLabel];

    // Create door cards
    NSArray *advisors = @[
        @{@"name": @"叙事节奏顾问", @"english": @"RhythmGuide", @"desc": @"Design storyboards with balanced pacing", @"color": [UIColor colorWithRed:0.2 green:0.3 blue:0.6 alpha:1.0]},
        @{@"name": @"动作分镜师", @"english": @"ActionFrame", @"desc": @"Create dynamic action sequences", @"color": [UIColor colorWithRed:0.8 green:0.2 blue:0.2 alpha:1.0]},
        @{@"name": @"对话分镜师", @"english": @"DialoguePanel", @"desc": @"Frame conversational scenes effectively", @"color": [UIColor colorWithRed:0.3 green:0.7 blue:0.3 alpha:1.0]},
        @{@"name": @"情感分镜顾问", @"english": @"EmotionFrame", @"desc": @"Convey mood through framing", @"color": [UIColor colorWithRed:0.7 green:0.3 blue:0.7 alpha:1.0]},
        @{@"name": @"构图顾问", @"english": @"ComposePro", @"desc": @"Teach strong panel composition", @"color": [UIColor colorWithRed:0.4 green:0.4 blue:0.8 alpha:1.0]}
    ];

    CGFloat cardWidth = 280;
    CGFloat cardHeight = 400;
    CGFloat spacing = 20;

    for (NSInteger i = 0; i < advisors.count; i++) {
        NSDictionary *advisor = advisors[i];
        UIView *doorCard = [self createDoorCardWithAdvisor:advisor];
        doorCard.translatesAutoresizingMaskIntoConstraints = NO;
        [scrollView addSubview:doorCard];

        [NSLayoutConstraint activateConstraints:@[
            [doorCard.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor constant:i * (cardWidth + spacing) + spacing],
            [doorCard.centerYAnchor constraintEqualToAnchor:scrollView.centerYAnchor],
            [doorCard.widthAnchor constraintEqualToConstant:cardWidth],
            [doorCard.heightAnchor constraintEqualToConstant:cardHeight]
        ]];
    }

    // Set scroll view content size
    CGFloat contentWidth = advisors.count * (cardWidth + spacing) + spacing;
    scrollView.contentSize = CGSizeMake(contentWidth, 0);

    // Knock button
    UIButton *knockButton = [[UIButton alloc] init];
    [knockButton setTitle:@"Knock" forState:UIControlStateNormal];
    [knockButton setTitleColor:CELRA_PRIMARY_BG forState:UIControlStateNormal];
    knockButton.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    knockButton.backgroundColor = CELRA_ACCENT;
    knockButton.layer.cornerRadius = 30;
    knockButton.layer.shadowColor = [UIColor blackColor].CGColor;
    knockButton.layer.shadowOffset = CGSizeMake(0, 4);
    knockButton.layer.shadowRadius = 8;
    knockButton.layer.shadowOpacity = 0.3;
    knockButton.translatesAutoresizingMaskIntoConstraints = NO;
    [knockButton addTarget:self action:@selector(knockButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:knockButton];

    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Title
        [titleLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:24],
        [titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],

        // Subtitle
        [subtitleLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:8],
        [subtitleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [subtitleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],

        // Scroll view
        [scrollView.topAnchor constraintEqualToAnchor:subtitleLabel.bottomAnchor constant:32],
        [scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [scrollView.heightAnchor constraintEqualToConstant:cardHeight],

        // Knock button
        [knockButton.topAnchor constraintEqualToAnchor:scrollView.bottomAnchor constant:32],
        [knockButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [knockButton.widthAnchor constraintEqualToConstant:120],
        [knockButton.heightAnchor constraintEqualToConstant:60]
    ]];
}

- (UIView *)createDoorCardWithAdvisor:(NSDictionary *)advisor {
    UIView *cardView = [[UIView alloc] init];
    cardView.backgroundColor = CELRA_CARD_BG;
    cardView.layer.cornerRadius = 16;
    cardView.layer.shadowColor = [UIColor blackColor].CGColor;
    cardView.layer.shadowOffset = CGSizeMake(0, 8);
    cardView.layer.shadowRadius = 16;
    cardView.layer.shadowOpacity = 0.3;

    // Door frame
    UIView *doorFrame = [[UIView alloc] init];
    doorFrame.backgroundColor = [UIColor colorWithRed:0.4 green:0.3 blue:0.2 alpha:1.0];
    doorFrame.layer.cornerRadius = 12;
    doorFrame.translatesAutoresizingMaskIntoConstraints = NO;
    [cardView addSubview:doorFrame];

    // Door
    UIView *door = [[UIView alloc] init];
    door.backgroundColor = advisor[@"color"];
    door.layer.cornerRadius = 8;
    door.layer.borderWidth = 2;
    door.layer.borderColor = [UIColor colorWithRed:0.3 green:0.2 blue:0.1 alpha:1.0].CGColor;
    door.translatesAutoresizingMaskIntoConstraints = NO;
    [doorFrame addSubview:door];

    // Door handle
    UIView *handle = [[UIView alloc] init];
    handle.backgroundColor = [UIColor colorWithRed:0.8 green:0.7 blue:0.3 alpha:1.0];
    handle.layer.cornerRadius = 6;
    handle.translatesAutoresizingMaskIntoConstraints = NO;
    [door addSubview:handle];

    // Peephole
    UIView *peephole = [[UIView alloc] init];
    peephole.backgroundColor = [UIColor colorWithRed:0.1 green:0.1 blue:0.1 alpha:1.0];
    peephole.layer.cornerRadius = 8;
    peephole.layer.borderWidth = 2;
    peephole.layer.borderColor = [UIColor colorWithRed:0.6 green:0.5 blue:0.2 alpha:1.0].CGColor;
    peephole.translatesAutoresizingMaskIntoConstraints = NO;
    [door addSubview:peephole];

    // AI indicator
    UIView *aiIndicator = [[UIView alloc] init];
    aiIndicator.backgroundColor = CELRA_ACCENT;
    aiIndicator.layer.cornerRadius = 3;
    aiIndicator.translatesAutoresizingMaskIntoConstraints = NO;
    [peephole addSubview:aiIndicator];

    // Name label
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.text = advisor[@"name"];
    nameLabel.font = [UIFont boldSystemFontOfSize:18];
    nameLabel.textColor = [UIColor whiteColor];
    nameLabel.textAlignment = NSTextAlignmentCenter;
    nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [cardView addSubview:nameLabel];

    // English name label
    UILabel *englishLabel = [[UILabel alloc] init];
    englishLabel.text = advisor[@"english"];
    englishLabel.font = [UIFont systemFontOfSize:16];
    englishLabel.textColor = CELRA_ACCENT;
    englishLabel.textAlignment = NSTextAlignmentCenter;
    englishLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [cardView addSubview:englishLabel];

    // Description label
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.text = advisor[@"desc"];
    descLabel.font = [UIFont systemFontOfSize:12];
    descLabel.textColor = CELRA_SECONDARY;
    descLabel.textAlignment = NSTextAlignmentCenter;
    descLabel.numberOfLines = 2;
    descLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [cardView addSubview:descLabel];

    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Door frame
        [doorFrame.topAnchor constraintEqualToAnchor:cardView.topAnchor constant:16],
        [doorFrame.centerXAnchor constraintEqualToAnchor:cardView.centerXAnchor],
        [doorFrame.widthAnchor constraintEqualToConstant:200],
        [doorFrame.heightAnchor constraintEqualToConstant:240],

        // Door
        [door.topAnchor constraintEqualToAnchor:doorFrame.topAnchor constant:8],
        [door.leadingAnchor constraintEqualToAnchor:doorFrame.leadingAnchor constant:8],
        [door.trailingAnchor constraintEqualToAnchor:doorFrame.trailingAnchor constant:-8],
        [door.bottomAnchor constraintEqualToAnchor:doorFrame.bottomAnchor constant:-8],

        // Handle
        [handle.trailingAnchor constraintEqualToAnchor:door.trailingAnchor constant:-16],
        [handle.centerYAnchor constraintEqualToAnchor:door.centerYAnchor],
        [handle.widthAnchor constraintEqualToConstant:12],
        [handle.heightAnchor constraintEqualToConstant:24],

        // Peephole
        [peephole.centerXAnchor constraintEqualToAnchor:door.centerXAnchor],
        [peephole.topAnchor constraintEqualToAnchor:door.topAnchor constant:32],
        [peephole.widthAnchor constraintEqualToConstant:16],
        [peephole.heightAnchor constraintEqualToConstant:16],

        // AI indicator
        [aiIndicator.centerXAnchor constraintEqualToAnchor:peephole.centerXAnchor],
        [aiIndicator.centerYAnchor constraintEqualToAnchor:peephole.centerYAnchor],
        [aiIndicator.widthAnchor constraintEqualToConstant:6],
        [aiIndicator.heightAnchor constraintEqualToConstant:6],

        // Labels
        [nameLabel.topAnchor constraintEqualToAnchor:doorFrame.bottomAnchor constant:16],
        [nameLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:8],
        [nameLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-8],

        [englishLabel.topAnchor constraintEqualToAnchor:nameLabel.bottomAnchor constant:4],
        [englishLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:8],
        [englishLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-8],

        [descLabel.topAnchor constraintEqualToAnchor:englishLabel.bottomAnchor constant:8],
        [descLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:8],
        [descLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-8]
    ]];

    return cardView;
}

- (void)knockButtonTapped:(UIButton *)sender {
    // Animate button
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.1 animations:^{
            sender.transform = CGAffineTransformIdentity;
        }];
    }];

    // Show alert
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Welcome!"
                                                                   message:@"This is a demo of the door interaction. In the full version, this would open a chat with the AI advisor."
                                                            preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
    [self presentViewController:alert animated:YES completion:nil];
}

@end

// MARK: - Profile View Controller Implementation
@implementation CelraProfileViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    self.view.backgroundColor = CELRA_PRIMARY_BG;
    self.navigationItem.title = @"My Desk";

    // Scroll view
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:scrollView];

    // Content view
    UIView *contentView = [[UIView alloc] init];
    contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [scrollView addSubview:contentView];

    // Title
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"My Creative Desk";
    titleLabel.font = [UIFont boldSystemFontOfSize:28];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:titleLabel];

    // Subtitle
    UILabel *subtitleLabel = [[UILabel alloc] init];
    subtitleLabel.text = @"Capture your storyboard ideas with voice notes";
    subtitleLabel.font = [UIFont systemFontOfSize:16];
    subtitleLabel.textColor = CELRA_SECONDARY;
    subtitleLabel.textAlignment = NSTextAlignmentCenter;
    subtitleLabel.numberOfLines = 2;
    subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:subtitleLabel];

    // Desk background
    UIView *deskView = [[UIView alloc] init];
    deskView.backgroundColor = [UIColor colorWithRed:0.4 green:0.3 blue:0.2 alpha:1.0];
    deskView.layer.cornerRadius = 16;
    deskView.layer.shadowColor = [UIColor blackColor].CGColor;
    deskView.layer.shadowOffset = CGSizeMake(0, 4);
    deskView.layer.shadowRadius = 12;
    deskView.layer.shadowOpacity = 0.3;
    deskView.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:deskView];

    // Coffee cup
    UIView *coffeeCup = [[UIView alloc] init];
    coffeeCup.backgroundColor = [UIColor colorWithRed:0.8 green:0.6 blue:0.4 alpha:1.0];
    coffeeCup.layer.cornerRadius = 15;
    coffeeCup.translatesAutoresizingMaskIntoConstraints = NO;
    [deskView addSubview:coffeeCup];

    // Pencil holder
    UIView *pencilHolder = [[UIView alloc] init];
    pencilHolder.backgroundColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    pencilHolder.layer.cornerRadius = 8;
    pencilHolder.translatesAutoresizingMaskIntoConstraints = NO;
    [deskView addSubview:pencilHolder];

    // Voice note area
    UIView *voiceNoteArea = [[UIView alloc] init];
    voiceNoteArea.backgroundColor = CELRA_CARD_BG;
    voiceNoteArea.layer.cornerRadius = 12;
    voiceNoteArea.translatesAutoresizingMaskIntoConstraints = NO;
    [deskView addSubview:voiceNoteArea];

    // Microphone button
    UIButton *micButton = [[UIButton alloc] init];
    [micButton setTitle:@"🎤" forState:UIControlStateNormal];
    micButton.titleLabel.font = [UIFont systemFontOfSize:24];
    micButton.backgroundColor = CELRA_ACCENT;
    micButton.layer.cornerRadius = 25;
    micButton.translatesAutoresizingMaskIntoConstraints = NO;
    [micButton addTarget:self action:@selector(micButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [voiceNoteArea addSubview:micButton];

    // Status label
    UILabel *statusLabel = [[UILabel alloc] init];
    statusLabel.text = @"Tap to record";
    statusLabel.font = [UIFont systemFontOfSize:12];
    statusLabel.textColor = [UIColor whiteColor];
    statusLabel.textAlignment = NSTextAlignmentCenter;
    statusLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [voiceNoteArea addSubview:statusLabel];

    // Demo notes
    UILabel *notesLabel = [[UILabel alloc] init];
    notesLabel.text = @"📝 Recent Voice Notes:\n\n• \"Wide shot of hero entering dark alley\"\n• \"Close-up on villain's menacing smile\"\n• \"Action sequence: chase through market\"\n• \"Emotional beat: character reflection\"";
    notesLabel.font = [UIFont systemFontOfSize:14];
    notesLabel.textColor = [UIColor whiteColor];
    notesLabel.numberOfLines = 0;
    notesLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:notesLabel];

    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Scroll view
        [scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [scrollView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor],

        // Content view
        [contentView.topAnchor constraintEqualToAnchor:scrollView.topAnchor],
        [contentView.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor],
        [contentView.trailingAnchor constraintEqualToAnchor:scrollView.trailingAnchor],
        [contentView.bottomAnchor constraintEqualToAnchor:scrollView.bottomAnchor],
        [contentView.widthAnchor constraintEqualToAnchor:scrollView.widthAnchor],

        // Title
        [titleLabel.topAnchor constraintEqualToAnchor:contentView.topAnchor constant:24],
        [titleLabel.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-16],

        // Subtitle
        [subtitleLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:8],
        [subtitleLabel.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:16],
        [subtitleLabel.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-16],

        // Desk
        [deskView.topAnchor constraintEqualToAnchor:subtitleLabel.bottomAnchor constant:24],
        [deskView.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:16],
        [deskView.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-16],
        [deskView.heightAnchor constraintEqualToConstant:200],

        // Coffee cup
        [coffeeCup.topAnchor constraintEqualToAnchor:deskView.topAnchor constant:16],
        [coffeeCup.trailingAnchor constraintEqualToAnchor:deskView.trailingAnchor constant:-16],
        [coffeeCup.widthAnchor constraintEqualToConstant:30],
        [coffeeCup.heightAnchor constraintEqualToConstant:30],

        // Pencil holder
        [pencilHolder.topAnchor constraintEqualToAnchor:deskView.topAnchor constant:16],
        [pencilHolder.leadingAnchor constraintEqualToAnchor:deskView.leadingAnchor constant:16],
        [pencilHolder.widthAnchor constraintEqualToConstant:20],
        [pencilHolder.heightAnchor constraintEqualToConstant:40],

        // Voice note area
        [voiceNoteArea.centerXAnchor constraintEqualToAnchor:deskView.centerXAnchor],
        [voiceNoteArea.centerYAnchor constraintEqualToAnchor:deskView.centerYAnchor],
        [voiceNoteArea.widthAnchor constraintEqualToConstant:150],
        [voiceNoteArea.heightAnchor constraintEqualToConstant:100],

        // Mic button
        [micButton.centerXAnchor constraintEqualToAnchor:voiceNoteArea.centerXAnchor],
        [micButton.topAnchor constraintEqualToAnchor:voiceNoteArea.topAnchor constant:16],
        [micButton.widthAnchor constraintEqualToConstant:50],
        [micButton.heightAnchor constraintEqualToConstant:50],

        // Status label
        [statusLabel.topAnchor constraintEqualToAnchor:micButton.bottomAnchor constant:8],
        [statusLabel.centerXAnchor constraintEqualToAnchor:voiceNoteArea.centerXAnchor],

        // Notes
        [notesLabel.topAnchor constraintEqualToAnchor:deskView.bottomAnchor constant:24],
        [notesLabel.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:16],
        [notesLabel.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-16],
        [notesLabel.bottomAnchor constraintEqualToAnchor:contentView.bottomAnchor constant:-24]
    ]];
}

- (void)micButtonTapped:(UIButton *)sender {
    // Animate button
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.1 animations:^{
            sender.transform = CGAffineTransformIdentity;
        }];
    }];

    // Show alert
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Voice Recording"
                                                                   message:@"This is a demo of the voice note feature. In the full version, this would record your voice and convert it to text using iOS Speech Recognition."
                                                            preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
    [self presentViewController:alert animated:YES completion:nil];
}

@end
