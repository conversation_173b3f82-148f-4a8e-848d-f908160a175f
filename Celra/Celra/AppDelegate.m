//
//  AppDelegate.m
//  Celra
//
//  Created by asde007 on 2025/8/4.
//

#import "AppDelegate.h"

@interface AppDelegate ()

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // Create window for all iOS versions
    self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];

    // Create a simple tab bar controller for now
    UITabBarController *tabBarController = [[UITabBarController alloc] init];

    // Create simple view controllers
    UIViewController *hallwayVC = [[UIViewController alloc] init];
    hallwayVC.view.backgroundColor = [UIColor colorWithRed:0.102 green:0.102 blue:0.180 alpha:1.0];
    hallwayVC.title = @"Advisors";
    hallwayVC.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"Advisors" image:nil tag:0];

    UIViewController *profileVC = [[UIViewController alloc] init];
    profileVC.view.backgroundColor = [UIColor colorWithRed:0.102 green:0.102 blue:0.180 alpha:1.0];
    profileVC.title = @"My Desk";
    profileVC.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"My Desk" image:nil tag:1];

    // Add welcome labels
    UILabel *hallwayLabel = [[UILabel alloc] init];
    hallwayLabel.text = @"Welcome to Celra\nAI Storyboard Advisors\n\nThis is a demo showing the app structure.\nThe full implementation includes:\n• 10 AI Characters\n• Interactive Door Cards\n• Voice Notes\n• Chat Interface";
    hallwayLabel.textColor = [UIColor whiteColor];
    hallwayLabel.textAlignment = NSTextAlignmentCenter;
    hallwayLabel.numberOfLines = 0;
    hallwayLabel.font = [UIFont systemFontOfSize:16];
    hallwayLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [hallwayVC.view addSubview:hallwayLabel];

    UILabel *profileLabel = [[UILabel alloc] init];
    profileLabel.text = @"My Creative Desk\n\nThis would contain:\n• Voice Note Recording\n• Speech-to-Text\n• Note Management\n• Personal Workspace";
    profileLabel.textColor = [UIColor whiteColor];
    profileLabel.textAlignment = NSTextAlignmentCenter;
    profileLabel.numberOfLines = 0;
    profileLabel.font = [UIFont systemFontOfSize:16];
    profileLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [profileVC.view addSubview:profileLabel];

    // Add constraints
    [NSLayoutConstraint activateConstraints:@[
        [hallwayLabel.centerXAnchor constraintEqualToAnchor:hallwayVC.view.centerXAnchor],
        [hallwayLabel.centerYAnchor constraintEqualToAnchor:hallwayVC.view.centerYAnchor],
        [hallwayLabel.leadingAnchor constraintEqualToAnchor:hallwayVC.view.leadingAnchor constant:20],
        [hallwayLabel.trailingAnchor constraintEqualToAnchor:hallwayVC.view.trailingAnchor constant:-20],

        [profileLabel.centerXAnchor constraintEqualToAnchor:profileVC.view.centerXAnchor],
        [profileLabel.centerYAnchor constraintEqualToAnchor:profileVC.view.centerYAnchor],
        [profileLabel.leadingAnchor constraintEqualToAnchor:profileVC.view.leadingAnchor constant:20],
        [profileLabel.trailingAnchor constraintEqualToAnchor:profileVC.view.trailingAnchor constant:-20]
    ]];

    // Set up tab bar
    tabBarController.viewControllers = @[hallwayVC, profileVC];
    tabBarController.tabBar.backgroundColor = [UIColor colorWithRed:0.102 green:0.102 blue:0.180 alpha:1.0];
    tabBarController.tabBar.tintColor = [UIColor colorWithRed:1.0 green:0.843 blue:0.0 alpha:1.0];
    tabBarController.tabBar.unselectedItemTintColor = [UIColor colorWithRed:0.918 green:0.918 blue:0.937 alpha:1.0];

    self.window.rootViewController = tabBarController;
    [self.window makeKeyAndVisible];

    return YES;
}

@end
