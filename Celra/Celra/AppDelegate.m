//
//  AppDelegate.m
//  Celra
//
//  Created by asde007 on 2025/8/4.
//

#import "AppDelegate.h"

// Design System Colors
#define CELRA_PRIMARY_BG [UIColor colorWithRed:0.102 green:0.102 blue:0.180 alpha:1.0]
#define CELRA_ACCENT [UIColor colorWithRed:1.0 green:0.843 blue:0.0 alpha:1.0]
#define CELRA_SECONDARY [UIColor colorWithRed:0.918 green:0.918 blue:0.937 alpha:1.0]
#define CELRA_CARD_BG [UIColor colorWithRed:0.15 green:0.15 blue:0.25 alpha:0.9]

@interface CelraHallwayViewController : UIViewController
@end

@interface CelraProfileViewController : UIViewController
@end

@interface CelraChatViewController : UIViewController
@property (nonatomic, strong) NSDictionary *advisor;
- (instancetype)initWithAdvisor:(NSDictionary *)advisor;
@end

@interface CelraDetailsViewController : UIViewController
@property (nonatomic, strong) NSDictionary *advisor;
- (instancetype)initWithAdvisor:(NSDictionary *)advisor;
@end

@interface AppDelegate ()

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // Create window for all iOS versions
    self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];

    // Create the main tab bar controller with beautiful UI
    UITabBarController *tabBarController = [[UITabBarController alloc] init];

    // Create hallway view controller
    CelraHallwayViewController *hallwayVC = [[CelraHallwayViewController alloc] init];
    UINavigationController *hallwayNav = [[UINavigationController alloc] initWithRootViewController:hallwayVC];
    hallwayNav.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"Advisors" image:nil tag:0];

    // Create profile view controller
    CelraProfileViewController *profileVC = [[CelraProfileViewController alloc] init];
    UINavigationController *profileNav = [[UINavigationController alloc] initWithRootViewController:profileVC];
    profileNav.tabBarItem = [[UITabBarItem alloc] initWithTitle:@"My Desk" image:nil tag:1];

    // Set view controllers
    tabBarController.viewControllers = @[hallwayNav, profileNav];

    // Customize tab bar appearance
    tabBarController.tabBar.backgroundColor = CELRA_PRIMARY_BG;
    tabBarController.tabBar.tintColor = CELRA_ACCENT;
    tabBarController.tabBar.unselectedItemTintColor = CELRA_SECONDARY;

    // Customize navigation bars
    [self customizeNavigationBar:hallwayNav.navigationBar];
    [self customizeNavigationBar:profileNav.navigationBar];

    self.window.rootViewController = tabBarController;
    [self.window makeKeyAndVisible];

    return YES;
}

- (void)customizeNavigationBar:(UINavigationBar *)navigationBar {
    navigationBar.backgroundColor = CELRA_PRIMARY_BG;
    navigationBar.tintColor = CELRA_ACCENT;
    navigationBar.barTintColor = CELRA_PRIMARY_BG;
    navigationBar.titleTextAttributes = @{
        NSForegroundColorAttributeName: [UIColor whiteColor],
        NSFontAttributeName: [UIFont boldSystemFontOfSize:20]
    };
}

@end

// MARK: - Hallway View Controller Implementation
@implementation CelraHallwayViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    // Create gradient background
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = self.view.bounds;
    gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.08 green:0.08 blue:0.15 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.15 green:0.15 blue:0.25 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.08 green:0.08 blue:0.15 alpha:1.0].CGColor
    ];
    gradientLayer.locations = @[@0.0, @0.5, @1.0];
    [self.view.layer insertSublayer:gradientLayer atIndex:0];

    self.view.backgroundColor = CELRA_PRIMARY_BG;
    self.navigationItem.title = @"AI Storyboard Advisors";

    // Create scroll view for door cards
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.backgroundColor = [UIColor clearColor];
    scrollView.showsHorizontalScrollIndicator = NO;
    scrollView.pagingEnabled = YES;
    scrollView.tag = 1000; // Tag for easy access
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:scrollView];

    // Title label with better styling
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Choose Your Advisor";
    titleLabel.font = [UIFont systemFontOfSize:32 weight:UIFontWeightBold];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:titleLabel];

    // Subtitle label with glow effect
    UILabel *subtitleLabel = [[UILabel alloc] init];
    subtitleLabel.text = @"Swipe to explore different expertise";
    subtitleLabel.font = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
    subtitleLabel.textColor = CELRA_ACCENT;
    subtitleLabel.textAlignment = NSTextAlignmentCenter;
    subtitleLabel.layer.shadowColor = CELRA_ACCENT.CGColor;
    subtitleLabel.layer.shadowOffset = CGSizeMake(0, 0);
    subtitleLabel.layer.shadowRadius = 10;
    subtitleLabel.layer.shadowOpacity = 0.5;
    subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:subtitleLabel];

    // Create door cards
    NSArray *advisors = @[
        @{@"name": @"Narrative Rhythm Advisor", @"english": @"RhythmGuide", @"desc": @"Design storyboards with balanced pacing", @"id": @"1"},
        @{@"name": @"Action Storyboard Artist", @"english": @"ActionFrame", @"desc": @"Create dynamic action sequences", @"id": @"2"},
        @{@"name": @"Dialogue Panel Artist", @"english": @"DialoguePanel", @"desc": @"Frame conversational scenes effectively", @"id": @"3"},
        @{@"name": @"Emotion Frame Advisor", @"english": @"EmotionFrame", @"desc": @"Convey mood through framing", @"id": @"4"},
        @{@"name": @"Composition Pro", @"english": @"ComposePro", @"desc": @"Teach strong panel composition", @"id": @"5"},
        @{@"name": @"Beginner Storyboard Guide", @"english": @"BeginnerGuide", @"desc": @"Basics for new storyboard artists", @"id": @"6"},
        @{@"name": @"Sci-Fi Frame Artist", @"english": @"SciFiFrame", @"desc": @"Storyboard sci-fi settings and elements", @"id": @"7"},
        @{@"name": @"Comedy Frame Advisor", @"english": @"ComedyFrame", @"desc": @"Create comedic timing in frames", @"id": @"8"},
        @{@"name": @"Digital Tool Pro", @"english": @"DigitalToolPro", @"desc": @"Recommend digital storyboard tools", @"id": @"9"},
        @{@"name": @"Revision Pro", @"english": @"RevisionPro", @"desc": @"Refine existing storyboards", @"id": @"10"}
    ];

    CGFloat cardWidth = 320;
    CGFloat cardHeight = 500;
    CGFloat spacing = 30;

    for (NSInteger i = 0; i < advisors.count; i++) {
        NSDictionary *advisor = advisors[i];
        UIView *doorCard = [self createDoorCardWithAdvisor:advisor];
        doorCard.translatesAutoresizingMaskIntoConstraints = NO;
        [scrollView addSubview:doorCard];

        [NSLayoutConstraint activateConstraints:@[
            [doorCard.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor constant:i * (cardWidth + spacing) + spacing],
            [doorCard.centerYAnchor constraintEqualToAnchor:scrollView.centerYAnchor],
            [doorCard.widthAnchor constraintEqualToConstant:cardWidth],
            [doorCard.heightAnchor constraintEqualToConstant:cardHeight]
        ]];
    }

    // Set scroll view content size
    CGFloat contentWidth = advisors.count * (cardWidth + spacing) + spacing;
    scrollView.contentSize = CGSizeMake(contentWidth, 0);

    // Knock button
    UIButton *knockButton = [[UIButton alloc] init];
    [knockButton setTitle:@"Knock" forState:UIControlStateNormal];
    [knockButton setTitleColor:CELRA_PRIMARY_BG forState:UIControlStateNormal];
    knockButton.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    knockButton.backgroundColor = CELRA_ACCENT;
    knockButton.layer.cornerRadius = 30;
    knockButton.layer.shadowColor = [UIColor blackColor].CGColor;
    knockButton.layer.shadowOffset = CGSizeMake(0, 4);
    knockButton.layer.shadowRadius = 8;
    knockButton.layer.shadowOpacity = 0.3;
    knockButton.translatesAutoresizingMaskIntoConstraints = NO;
    [knockButton addTarget:self action:@selector(knockButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:knockButton];

    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Title
        [titleLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:24],
        [titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],

        // Subtitle
        [subtitleLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:8],
        [subtitleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [subtitleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],

        // Scroll view
        [scrollView.topAnchor constraintEqualToAnchor:subtitleLabel.bottomAnchor constant:32],
        [scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [scrollView.heightAnchor constraintEqualToConstant:cardHeight],

        // Knock button
        [knockButton.topAnchor constraintEqualToAnchor:scrollView.bottomAnchor constant:32],
        [knockButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [knockButton.widthAnchor constraintEqualToConstant:120],
        [knockButton.heightAnchor constraintEqualToConstant:60]
    ]];
}

- (UIView *)createDoorCardWithAdvisor:(NSDictionary *)advisor {
    UIView *cardView = [[UIView alloc] init];
    cardView.backgroundColor = [UIColor clearColor];
    cardView.tag = [advisor[@"id"] integerValue]; // Store advisor ID for later reference

    // Main card container with glass morphism effect
    UIView *cardContainer = [[UIView alloc] init];
    cardContainer.backgroundColor = [UIColor colorWithRed:1.0 green:1.0 blue:1.0 alpha:0.05];
    cardContainer.layer.cornerRadius = 28;
    cardContainer.layer.borderWidth = 1.5;
    cardContainer.layer.borderColor = [UIColor colorWithRed:1.0 green:1.0 blue:1.0 alpha:0.15].CGColor;

    // Add shadow
    cardContainer.layer.shadowColor = [UIColor blackColor].CGColor;
    cardContainer.layer.shadowOffset = CGSizeMake(0, 15);
    cardContainer.layer.shadowRadius = 35;
    cardContainer.layer.shadowOpacity = 0.4;

    // Add backdrop blur effect
    UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:[UIBlurEffect effectWithStyle:UIBlurEffectStyleDark]];
    blurView.layer.cornerRadius = 28;
    blurView.clipsToBounds = YES;
    blurView.translatesAutoresizingMaskIntoConstraints = NO;
    [cardContainer addSubview:blurView];

    cardContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [cardView addSubview:cardContainer];

    // Door/Character image container
    UIView *imageContainer = [[UIView alloc] init];
    imageContainer.backgroundColor = [UIColor clearColor];
    imageContainer.layer.cornerRadius = 20;
    imageContainer.clipsToBounds = YES;
    imageContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [cardContainer addSubview:imageContainer];

    // Door image view
    UIImageView *doorImageView = [[UIImageView alloc] init];
    doorImageView.image = [UIImage imageNamed:@"door"];
    doorImageView.contentMode = UIViewContentModeScaleAspectFill;
    doorImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [imageContainer addSubview:doorImageView];

    // Character image view (initially hidden)
    UIImageView *characterImageView = [[UIImageView alloc] init];
    characterImageView.image = [UIImage imageNamed:advisor[@"id"]];
    characterImageView.contentMode = UIViewContentModeScaleAspectFill;
    characterImageView.alpha = 0.0;
    characterImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [imageContainer addSubview:characterImageView];

    // Store references for animation
    doorImageView.tag = 100;
    characterImageView.tag = 200;
    imageContainer.tag = 300;

    // Info section with glass effect
    UIView *infoSection = [[UIView alloc] init];
    infoSection.backgroundColor = [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.2];
    infoSection.layer.cornerRadius = 20;
    infoSection.layer.borderWidth = 1;
    infoSection.layer.borderColor = [UIColor colorWithRed:1.0 green:1.0 blue:1.0 alpha:0.1].CGColor;
    infoSection.translatesAutoresizingMaskIntoConstraints = NO;
    [cardContainer addSubview:infoSection];

    // Name label with better typography
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.text = advisor[@"name"];
    nameLabel.font = [UIFont systemFontOfSize:24 weight:UIFontWeightBold];
    nameLabel.textColor = [UIColor whiteColor];
    nameLabel.textAlignment = NSTextAlignmentCenter;
    nameLabel.numberOfLines = 2;
    nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [infoSection addSubview:nameLabel];

    // English name with glow effect
    UILabel *englishLabel = [[UILabel alloc] init];
    englishLabel.text = advisor[@"english"];
    englishLabel.font = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
    englishLabel.textColor = CELRA_ACCENT;
    englishLabel.textAlignment = NSTextAlignmentCenter;
    englishLabel.layer.shadowColor = CELRA_ACCENT.CGColor;
    englishLabel.layer.shadowOffset = CGSizeMake(0, 0);
    englishLabel.layer.shadowRadius = 10;
    englishLabel.layer.shadowOpacity = 0.8;
    englishLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [infoSection addSubview:englishLabel];

    // Description with better styling
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.text = advisor[@"desc"];
    descLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
    descLabel.textColor = [UIColor colorWithRed:1.0 green:1.0 blue:1.0 alpha:0.85];
    descLabel.textAlignment = NSTextAlignmentCenter;
    descLabel.numberOfLines = 3;
    descLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [infoSection addSubview:descLabel];

    // Knock button with premium styling
    UIButton *knockButton = [[UIButton alloc] init];
    [knockButton setTitle:@"🚪 Knock" forState:UIControlStateNormal];
    knockButton.backgroundColor = CELRA_ACCENT;
    [knockButton setTitleColor:[UIColor colorWithRed:0.08 green:0.08 blue:0.15 alpha:1.0] forState:UIControlStateNormal];
    knockButton.titleLabel.font = [UIFont systemFontOfSize:18 weight:UIFontWeightBold];
    knockButton.layer.cornerRadius = 30;
    knockButton.layer.shadowColor = CELRA_ACCENT.CGColor;
    knockButton.layer.shadowOffset = CGSizeMake(0, 6);
    knockButton.layer.shadowRadius = 15;
    knockButton.layer.shadowOpacity = 0.5;
    knockButton.translatesAutoresizingMaskIntoConstraints = NO;
    [knockButton addTarget:self action:@selector(knockButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [cardContainer addSubview:knockButton];

    // Action buttons container (initially hidden)
    UIView *actionContainer = [[UIView alloc] init];
    actionContainer.backgroundColor = [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.3];
    actionContainer.layer.cornerRadius = 20;
    actionContainer.layer.borderWidth = 2;
    actionContainer.layer.borderColor = CELRA_ACCENT.CGColor;
    actionContainer.alpha = 0.0;
    actionContainer.tag = 400;
    actionContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [cardContainer addSubview:actionContainer];

    // Chat button
    UIButton *chatButton = [[UIButton alloc] init];
    [chatButton setTitle:@"💬 Start Chat" forState:UIControlStateNormal];
    chatButton.backgroundColor = CELRA_ACCENT;
    [chatButton setTitleColor:[UIColor colorWithRed:0.08 green:0.08 blue:0.15 alpha:1.0] forState:UIControlStateNormal];
    chatButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightBold];
    chatButton.layer.cornerRadius = 25;
    chatButton.translatesAutoresizingMaskIntoConstraints = NO;
    [chatButton addTarget:self action:@selector(chatButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [actionContainer addSubview:chatButton];

    // Details button
    UIButton *detailsButton = [[UIButton alloc] init];
    [detailsButton setTitle:@"📖 View Details" forState:UIControlStateNormal];
    detailsButton.backgroundColor = [UIColor clearColor];
    detailsButton.layer.borderWidth = 2;
    detailsButton.layer.borderColor = CELRA_ACCENT.CGColor;
    [detailsButton setTitleColor:CELRA_ACCENT forState:UIControlStateNormal];
    detailsButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightBold];
    detailsButton.layer.cornerRadius = 25;
    detailsButton.translatesAutoresizingMaskIntoConstraints = NO;
    [detailsButton addTarget:self action:@selector(detailsButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [actionContainer addSubview:detailsButton];

    // Close button
    UIButton *closeButton = [[UIButton alloc] init];
    [closeButton setTitle:@"✕ Close Door" forState:UIControlStateNormal];
    closeButton.backgroundColor = [UIColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:0.3];
    [closeButton setTitleColor:[UIColor colorWithRed:1.0 green:1.0 blue:1.0 alpha:0.8] forState:UIControlStateNormal];
    closeButton.titleLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
    closeButton.layer.cornerRadius = 20;
    closeButton.translatesAutoresizingMaskIntoConstraints = NO;
    [closeButton addTarget:self action:@selector(closeDoorButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [actionContainer addSubview:closeButton];

    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Card container
        [cardContainer.topAnchor constraintEqualToAnchor:cardView.topAnchor constant:10],
        [cardContainer.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:10],
        [cardContainer.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-10],
        [cardContainer.bottomAnchor constraintEqualToAnchor:cardView.bottomAnchor constant:-10],

        // Blur view
        [blurView.topAnchor constraintEqualToAnchor:cardContainer.topAnchor],
        [blurView.leadingAnchor constraintEqualToAnchor:cardContainer.leadingAnchor],
        [blurView.trailingAnchor constraintEqualToAnchor:cardContainer.trailingAnchor],
        [blurView.bottomAnchor constraintEqualToAnchor:cardContainer.bottomAnchor],

        // Image container
        [imageContainer.topAnchor constraintEqualToAnchor:cardContainer.topAnchor constant:20],
        [imageContainer.leadingAnchor constraintEqualToAnchor:cardContainer.leadingAnchor constant:20],
        [imageContainer.trailingAnchor constraintEqualToAnchor:cardContainer.trailingAnchor constant:-20],
        [imageContainer.heightAnchor constraintEqualToConstant:350],

        // Door and character images
        [doorImageView.topAnchor constraintEqualToAnchor:imageContainer.topAnchor],
        [doorImageView.leadingAnchor constraintEqualToAnchor:imageContainer.leadingAnchor],
        [doorImageView.trailingAnchor constraintEqualToAnchor:imageContainer.trailingAnchor],
        [doorImageView.bottomAnchor constraintEqualToAnchor:imageContainer.bottomAnchor],

        [characterImageView.topAnchor constraintEqualToAnchor:imageContainer.topAnchor],
        [characterImageView.leadingAnchor constraintEqualToAnchor:imageContainer.leadingAnchor],
        [characterImageView.trailingAnchor constraintEqualToAnchor:imageContainer.trailingAnchor],
        [characterImageView.bottomAnchor constraintEqualToAnchor:imageContainer.bottomAnchor],

        // Info section
        [infoSection.topAnchor constraintEqualToAnchor:imageContainer.bottomAnchor constant:20],
        [infoSection.leadingAnchor constraintEqualToAnchor:cardContainer.leadingAnchor constant:20],
        [infoSection.trailingAnchor constraintEqualToAnchor:cardContainer.trailingAnchor constant:-20],
        [infoSection.heightAnchor constraintEqualToConstant:140],

        // Labels in info section
        [nameLabel.topAnchor constraintEqualToAnchor:infoSection.topAnchor constant:16],
        [nameLabel.leadingAnchor constraintEqualToAnchor:infoSection.leadingAnchor constant:16],
        [nameLabel.trailingAnchor constraintEqualToAnchor:infoSection.trailingAnchor constant:-16],

        [englishLabel.topAnchor constraintEqualToAnchor:nameLabel.bottomAnchor constant:8],
        [englishLabel.leadingAnchor constraintEqualToAnchor:infoSection.leadingAnchor constant:16],
        [englishLabel.trailingAnchor constraintEqualToAnchor:infoSection.trailingAnchor constant:-16],

        [descLabel.topAnchor constraintEqualToAnchor:englishLabel.bottomAnchor constant:12],
        [descLabel.leadingAnchor constraintEqualToAnchor:infoSection.leadingAnchor constant:16],
        [descLabel.trailingAnchor constraintEqualToAnchor:infoSection.trailingAnchor constant:-16],
        [descLabel.bottomAnchor constraintEqualToAnchor:infoSection.bottomAnchor constant:-16],

        // Knock button
        [knockButton.topAnchor constraintEqualToAnchor:infoSection.bottomAnchor constant:20],
        [knockButton.centerXAnchor constraintEqualToAnchor:cardContainer.centerXAnchor],
        [knockButton.widthAnchor constraintEqualToConstant:200],
        [knockButton.heightAnchor constraintEqualToConstant:60],
        [knockButton.bottomAnchor constraintEqualToAnchor:cardContainer.bottomAnchor constant:-20],

        // Action container (same position as knock button)
        [actionContainer.topAnchor constraintEqualToAnchor:infoSection.bottomAnchor constant:20],
        [actionContainer.leadingAnchor constraintEqualToAnchor:cardContainer.leadingAnchor constant:20],
        [actionContainer.trailingAnchor constraintEqualToAnchor:cardContainer.trailingAnchor constant:-20],
        [actionContainer.bottomAnchor constraintEqualToAnchor:cardContainer.bottomAnchor constant:-20],

        // Action buttons
        [chatButton.topAnchor constraintEqualToAnchor:actionContainer.topAnchor constant:16],
        [chatButton.leadingAnchor constraintEqualToAnchor:actionContainer.leadingAnchor constant:16],
        [chatButton.trailingAnchor constraintEqualToAnchor:actionContainer.trailingAnchor constant:-16],
        [chatButton.heightAnchor constraintEqualToConstant:50],

        [detailsButton.topAnchor constraintEqualToAnchor:chatButton.bottomAnchor constant:12],
        [detailsButton.leadingAnchor constraintEqualToAnchor:actionContainer.leadingAnchor constant:16],
        [detailsButton.trailingAnchor constraintEqualToAnchor:actionContainer.trailingAnchor constant:-16],
        [detailsButton.heightAnchor constraintEqualToConstant:50],

        [closeButton.topAnchor constraintEqualToAnchor:detailsButton.bottomAnchor constant:12],
        [closeButton.centerXAnchor constraintEqualToAnchor:actionContainer.centerXAnchor],
        [closeButton.widthAnchor constraintEqualToConstant:150],
        [closeButton.heightAnchor constraintEqualToConstant:40],
        [closeButton.bottomAnchor constraintEqualToAnchor:actionContainer.bottomAnchor constant:-16]
    ]];

    return cardView;
}

- (void)knockButtonTapped:(UIButton *)sender {
    // Animate button
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.1 animations:^{
            sender.transform = CGAffineTransformIdentity;
        }];
    }];

    // Get current door card (center one)
    UIScrollView *scrollView = (UIScrollView *)[self.view viewWithTag:1000];
    CGFloat cardWidth = 320;
    CGFloat spacing = 30;
    CGFloat pageWidth = cardWidth + spacing;
    NSInteger currentIndex = (NSInteger)(scrollView.contentOffset.x / pageWidth);

    // Find the current door card view
    UIView *currentCard = nil;
    for (UIView *subview in scrollView.subviews) {
        if ([subview isKindOfClass:[UIView class]] && subview.tag > 0 && subview.tag <= 10) {
            CGRect frame = subview.frame;
            CGFloat cardCenter = frame.origin.x + frame.size.width / 2;
            CGFloat scrollCenter = scrollView.contentOffset.x + scrollView.frame.size.width / 2;
            if (fabs(cardCenter - scrollCenter) < pageWidth / 2) {
                currentCard = subview;
                break;
            }
        }
    }

    if (currentCard) {
        [self openDoorWithCard:currentCard];
    }
}

- (void)openDoorWithCard:(UIView *)cardView {
    // Find the card container and its elements
    UIView *cardContainer = cardView.subviews.firstObject;
    UIView *imageContainer = [cardContainer viewWithTag:300];
    UIView *actionContainer = [cardContainer viewWithTag:400];
    UIImageView *doorImageView = [imageContainer viewWithTag:100];
    UIImageView *characterImageView = [imageContainer viewWithTag:200];

    // Find knock button
    UIButton *knockButton = nil;
    for (UIView *subview in cardContainer.subviews) {
        if ([subview isKindOfClass:[UIButton class]] && subview.tag != 400) {
            knockButton = (UIButton *)subview;
            break;
        }
    }

    // Door opening animation with better effects
    [UIView animateWithDuration:0.8 delay:0 options:UIViewAnimationOptionCurveEaseInOut animations:^{
        // Door slides out dramatically
        doorImageView.transform = CGAffineTransformMakeTranslation(-400, 0);
        doorImageView.alpha = 0.0;

        // Character appears with scale effect
        characterImageView.alpha = 1.0;
        characterImageView.transform = CGAffineTransformMakeScale(1.02, 1.02);

        // Hide knock button
        if (knockButton) {
            knockButton.alpha = 0.0;
            knockButton.transform = CGAffineTransformMakeScale(0.8, 0.8);
        }

    } completion:^(BOOL finished) {
        // Show action buttons with staggered animation
        [UIView animateWithDuration:0.5 delay:0.1 options:UIViewAnimationOptionCurveEaseOut animations:^{
            actionContainer.alpha = 1.0;
            actionContainer.transform = CGAffineTransformMakeTranslation(0, -5);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.2 animations:^{
                actionContainer.transform = CGAffineTransformIdentity;
                characterImageView.transform = CGAffineTransformIdentity;
            }];
        }];
    }];
}

// Get advisors data
- (NSArray *)getAdvisors {
    return @[
        @{@"name": @"Narrative Rhythm Advisor", @"english": @"RhythmGuide", @"desc": @"Design storyboards with balanced pacing", @"id": @"1"},
        @{@"name": @"Action Storyboard Artist", @"english": @"ActionFrame", @"desc": @"Create dynamic action sequences", @"id": @"2"},
        @{@"name": @"Dialogue Panel Artist", @"english": @"DialoguePanel", @"desc": @"Frame conversational scenes effectively", @"id": @"3"},
        @{@"name": @"Emotion Frame Advisor", @"english": @"EmotionFrame", @"desc": @"Convey mood through framing", @"id": @"4"},
        @{@"name": @"Composition Pro", @"english": @"ComposePro", @"desc": @"Teach strong panel composition", @"id": @"5"},
        @{@"name": @"Beginner Storyboard Guide", @"english": @"BeginnerGuide", @"desc": @"Basics for new storyboard artists", @"id": @"6"},
        @{@"name": @"Sci-Fi Frame Artist", @"english": @"SciFiFrame", @"desc": @"Storyboard sci-fi settings and elements", @"id": @"7"},
        @{@"name": @"Comedy Frame Advisor", @"english": @"ComedyFrame", @"desc": @"Create comedic timing in frames", @"id": @"8"},
        @{@"name": @"Digital Tool Pro", @"english": @"DigitalToolPro", @"desc": @"Recommend digital storyboard tools", @"id": @"9"},
        @{@"name": @"Revision Pro", @"english": @"RevisionPro", @"desc": @"Refine existing storyboards", @"id": @"10"}
    ];
}

// Button action methods
- (void)chatButtonTapped:(UIButton *)sender {
    UIView *cardView = [self findCardViewForButton:sender];
    if (cardView) {
        NSInteger advisorId = cardView.tag;
        NSArray *advisors = [self getAdvisors];
        NSDictionary *advisor = advisors[advisorId - 1];
        [self openChatWithAdvisor:advisor];
    }
}

- (void)detailsButtonTapped:(UIButton *)sender {
    UIView *cardView = [self findCardViewForButton:sender];
    if (cardView) {
        NSInteger advisorId = cardView.tag;
        NSArray *advisors = [self getAdvisors];
        NSDictionary *advisor = advisors[advisorId - 1];
        [self openDetailsWithAdvisor:advisor];
    }
}

- (void)closeDoorButtonTapped:(UIButton *)sender {
    UIView *cardView = [self findCardViewForButton:sender];
    if (cardView) {
        [self closeDoorWithCard:cardView];
    }
}

- (UIView *)findCardViewForButton:(UIButton *)button {
    UIView *view = button.superview;
    while (view && view.tag == 0) {
        view = view.superview;
    }
    return view;
}

- (void)closeDoorWithCard:(UIView *)cardView {
    // Find the card container and its elements
    UIView *cardContainer = cardView.subviews.firstObject;
    UIView *imageContainer = [cardContainer viewWithTag:300];
    UIView *actionContainer = [cardContainer viewWithTag:400];
    UIImageView *doorImageView = [imageContainer viewWithTag:100];
    UIImageView *characterImageView = [imageContainer viewWithTag:200];

    // Find knock button
    UIButton *knockButton = nil;
    for (UIView *subview in cardContainer.subviews) {
        if ([subview isKindOfClass:[UIButton class]] && subview.tag != 400) {
            knockButton = (UIButton *)subview;
            break;
        }
    }

    // Hide action buttons first
    [UIView animateWithDuration:0.3 animations:^{
        actionContainer.alpha = 0.0;
        actionContainer.transform = CGAffineTransformMakeTranslation(0, 20);
    } completion:^(BOOL finished) {
        // Then close the door
        [UIView animateWithDuration:0.6 delay:0.1 options:UIViewAnimationOptionCurveEaseInOut animations:^{
            doorImageView.transform = CGAffineTransformIdentity;
            doorImageView.alpha = 1.0;
            characterImageView.alpha = 0.0;

            // Show knock button again
            if (knockButton) {
                knockButton.alpha = 1.0;
                knockButton.transform = CGAffineTransformIdentity;
            }
        } completion:^(BOOL finished) {
            // Reset action container position for next time
            actionContainer.transform = CGAffineTransformIdentity;
        }];
    }];
}

- (void)openChatWithAdvisor:(NSDictionary *)advisor {
    // Create and present chat view controller
    CelraChatViewController *chatVC = [[CelraChatViewController alloc] initWithAdvisor:advisor];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:chatVC];
    [self presentViewController:navController animated:YES completion:nil];
}

- (void)openDetailsWithAdvisor:(NSDictionary *)advisor {
    // Create and present details view controller
    CelraDetailsViewController *detailsVC = [[CelraDetailsViewController alloc] initWithAdvisor:advisor];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:detailsVC];
    [self presentViewController:navController animated:YES completion:nil];
}

@end

// MARK: - Profile View Controller Implementation
@implementation CelraProfileViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    self.view.backgroundColor = CELRA_PRIMARY_BG;
    self.navigationItem.title = @"My Desk";

    // Scroll view
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:scrollView];

    // Content view
    UIView *contentView = [[UIView alloc] init];
    contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [scrollView addSubview:contentView];

    // Title
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"My Creative Desk";
    titleLabel.font = [UIFont boldSystemFontOfSize:28];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:titleLabel];

    // Subtitle
    UILabel *subtitleLabel = [[UILabel alloc] init];
    subtitleLabel.text = @"Capture your storyboard ideas with voice notes";
    subtitleLabel.font = [UIFont systemFontOfSize:16];
    subtitleLabel.textColor = CELRA_SECONDARY;
    subtitleLabel.textAlignment = NSTextAlignmentCenter;
    subtitleLabel.numberOfLines = 2;
    subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:subtitleLabel];

    // Desk background
    UIView *deskView = [[UIView alloc] init];
    deskView.backgroundColor = [UIColor colorWithRed:0.4 green:0.3 blue:0.2 alpha:1.0];
    deskView.layer.cornerRadius = 16;
    deskView.layer.shadowColor = [UIColor blackColor].CGColor;
    deskView.layer.shadowOffset = CGSizeMake(0, 4);
    deskView.layer.shadowRadius = 12;
    deskView.layer.shadowOpacity = 0.3;
    deskView.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:deskView];

    // Coffee cup
    UIView *coffeeCup = [[UIView alloc] init];
    coffeeCup.backgroundColor = [UIColor colorWithRed:0.8 green:0.6 blue:0.4 alpha:1.0];
    coffeeCup.layer.cornerRadius = 15;
    coffeeCup.translatesAutoresizingMaskIntoConstraints = NO;
    [deskView addSubview:coffeeCup];

    // Pencil holder
    UIView *pencilHolder = [[UIView alloc] init];
    pencilHolder.backgroundColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    pencilHolder.layer.cornerRadius = 8;
    pencilHolder.translatesAutoresizingMaskIntoConstraints = NO;
    [deskView addSubview:pencilHolder];

    // Voice note area
    UIView *voiceNoteArea = [[UIView alloc] init];
    voiceNoteArea.backgroundColor = CELRA_CARD_BG;
    voiceNoteArea.layer.cornerRadius = 12;
    voiceNoteArea.translatesAutoresizingMaskIntoConstraints = NO;
    [deskView addSubview:voiceNoteArea];

    // Microphone button
    UIButton *micButton = [[UIButton alloc] init];
    [micButton setTitle:@"🎤" forState:UIControlStateNormal];
    micButton.titleLabel.font = [UIFont systemFontOfSize:24];
    micButton.backgroundColor = CELRA_ACCENT;
    micButton.layer.cornerRadius = 25;
    micButton.translatesAutoresizingMaskIntoConstraints = NO;
    [micButton addTarget:self action:@selector(micButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [voiceNoteArea addSubview:micButton];

    // Status label
    UILabel *statusLabel = [[UILabel alloc] init];
    statusLabel.text = @"Tap to record";
    statusLabel.font = [UIFont systemFontOfSize:12];
    statusLabel.textColor = [UIColor whiteColor];
    statusLabel.textAlignment = NSTextAlignmentCenter;
    statusLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [voiceNoteArea addSubview:statusLabel];

    // Demo notes
    UILabel *notesLabel = [[UILabel alloc] init];
    notesLabel.text = @"📝 Recent Voice Notes:\n\n• \"Wide shot of hero entering dark alley\"\n• \"Close-up on villain's menacing smile\"\n• \"Action sequence: chase through market\"\n• \"Emotional beat: character reflection\"";
    notesLabel.font = [UIFont systemFontOfSize:14];
    notesLabel.textColor = [UIColor whiteColor];
    notesLabel.numberOfLines = 0;
    notesLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:notesLabel];

    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Scroll view
        [scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [scrollView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor],

        // Content view
        [contentView.topAnchor constraintEqualToAnchor:scrollView.topAnchor],
        [contentView.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor],
        [contentView.trailingAnchor constraintEqualToAnchor:scrollView.trailingAnchor],
        [contentView.bottomAnchor constraintEqualToAnchor:scrollView.bottomAnchor],
        [contentView.widthAnchor constraintEqualToAnchor:scrollView.widthAnchor],

        // Title
        [titleLabel.topAnchor constraintEqualToAnchor:contentView.topAnchor constant:24],
        [titleLabel.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:16],
        [titleLabel.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-16],

        // Subtitle
        [subtitleLabel.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:8],
        [subtitleLabel.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:16],
        [subtitleLabel.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-16],

        // Desk
        [deskView.topAnchor constraintEqualToAnchor:subtitleLabel.bottomAnchor constant:24],
        [deskView.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:16],
        [deskView.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-16],
        [deskView.heightAnchor constraintEqualToConstant:200],

        // Coffee cup
        [coffeeCup.topAnchor constraintEqualToAnchor:deskView.topAnchor constant:16],
        [coffeeCup.trailingAnchor constraintEqualToAnchor:deskView.trailingAnchor constant:-16],
        [coffeeCup.widthAnchor constraintEqualToConstant:30],
        [coffeeCup.heightAnchor constraintEqualToConstant:30],

        // Pencil holder
        [pencilHolder.topAnchor constraintEqualToAnchor:deskView.topAnchor constant:16],
        [pencilHolder.leadingAnchor constraintEqualToAnchor:deskView.leadingAnchor constant:16],
        [pencilHolder.widthAnchor constraintEqualToConstant:20],
        [pencilHolder.heightAnchor constraintEqualToConstant:40],

        // Voice note area
        [voiceNoteArea.centerXAnchor constraintEqualToAnchor:deskView.centerXAnchor],
        [voiceNoteArea.centerYAnchor constraintEqualToAnchor:deskView.centerYAnchor],
        [voiceNoteArea.widthAnchor constraintEqualToConstant:150],
        [voiceNoteArea.heightAnchor constraintEqualToConstant:100],

        // Mic button
        [micButton.centerXAnchor constraintEqualToAnchor:voiceNoteArea.centerXAnchor],
        [micButton.topAnchor constraintEqualToAnchor:voiceNoteArea.topAnchor constant:16],
        [micButton.widthAnchor constraintEqualToConstant:50],
        [micButton.heightAnchor constraintEqualToConstant:50],

        // Status label
        [statusLabel.topAnchor constraintEqualToAnchor:micButton.bottomAnchor constant:8],
        [statusLabel.centerXAnchor constraintEqualToAnchor:voiceNoteArea.centerXAnchor],

        // Notes
        [notesLabel.topAnchor constraintEqualToAnchor:deskView.bottomAnchor constant:24],
        [notesLabel.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:16],
        [notesLabel.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-16],
        [notesLabel.bottomAnchor constraintEqualToAnchor:contentView.bottomAnchor constant:-24]
    ]];
}

- (void)micButtonTapped:(UIButton *)sender {
    // Animate button
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.1 animations:^{
            sender.transform = CGAffineTransformIdentity;
        }];
    }];

    // Show alert
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Voice Recording"
                                                                   message:@"This is a demo of the voice note feature. In the full version, this would record your voice and convert it to text using iOS Speech Recognition."
                                                            preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
    [self presentViewController:alert animated:YES completion:nil];
}

@end

// MARK: - Chat View Controller

@implementation CelraChatViewController

- (instancetype)initWithAdvisor:(NSDictionary *)advisor {
    self = [super init];
    if (self) {
        _advisor = advisor;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupChatUI];
}

- (void)setupChatUI {
    // Set character image as background
    UIImageView *backgroundImageView = [[UIImageView alloc] init];
    backgroundImageView.image = [UIImage imageNamed:self.advisor[@"id"]];
    backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    backgroundImageView.alpha = 0.3;
    backgroundImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:backgroundImageView];

    // Dark overlay for readability
    UIView *overlay = [[UIView alloc] init];
    overlay.backgroundColor = [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.6];
    overlay.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:overlay];

    self.view.backgroundColor = CELRA_PRIMARY_BG;
    self.navigationItem.title = self.advisor[@"english"];
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"Close" style:UIBarButtonItemStylePlain target:self action:@selector(closeChat)];

    // Chat interface
    UILabel *welcomeLabel = [[UILabel alloc] init];
    welcomeLabel.text = [NSString stringWithFormat:@"Hello! I'm %@.\n\n%@\n\nHow can I help you with your storyboard today?", self.advisor[@"english"], self.advisor[@"desc"]];
    welcomeLabel.font = [UIFont systemFontOfSize:18];
    welcomeLabel.textColor = [UIColor whiteColor];
    welcomeLabel.numberOfLines = 0;
    welcomeLabel.textAlignment = NSTextAlignmentCenter;
    welcomeLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:welcomeLabel];

    // Input area
    UIView *inputContainer = [[UIView alloc] init];
    inputContainer.backgroundColor = CELRA_CARD_BG;
    inputContainer.layer.cornerRadius = 12;
    inputContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:inputContainer];

    UITextField *messageField = [[UITextField alloc] init];
    messageField.placeholder = @"Type your message...";
    messageField.textColor = [UIColor whiteColor];
    messageField.font = [UIFont systemFontOfSize:16];
    messageField.borderStyle = UITextBorderStyleNone;
    messageField.translatesAutoresizingMaskIntoConstraints = NO;
    [inputContainer addSubview:messageField];

    UIButton *sendButton = [[UIButton alloc] init];
    [sendButton setTitle:@"Send" forState:UIControlStateNormal];
    sendButton.backgroundColor = CELRA_ACCENT;
    sendButton.titleLabel.font = [UIFont boldSystemFontOfSize:16];
    [sendButton setTitleColor:CELRA_PRIMARY_BG forState:UIControlStateNormal];
    sendButton.layer.cornerRadius = 8;
    sendButton.translatesAutoresizingMaskIntoConstraints = NO;
    [inputContainer addSubview:sendButton];

    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Background image
        [backgroundImageView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [backgroundImageView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [backgroundImageView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [backgroundImageView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        // Overlay
        [overlay.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [overlay.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [overlay.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [overlay.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        // Welcome label
        [welcomeLabel.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [welcomeLabel.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor],
        [welcomeLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [welcomeLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],

        // Input container
        [inputContainer.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:16],
        [inputContainer.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-16],
        [inputContainer.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-16],
        [inputContainer.heightAnchor constraintEqualToConstant:60],

        // Message field
        [messageField.leadingAnchor constraintEqualToAnchor:inputContainer.leadingAnchor constant:16],
        [messageField.centerYAnchor constraintEqualToAnchor:inputContainer.centerYAnchor],
        [messageField.trailingAnchor constraintEqualToAnchor:sendButton.leadingAnchor constant:-12],

        // Send button
        [sendButton.trailingAnchor constraintEqualToAnchor:inputContainer.trailingAnchor constant:-16],
        [sendButton.centerYAnchor constraintEqualToAnchor:inputContainer.centerYAnchor],
        [sendButton.widthAnchor constraintEqualToConstant:60],
        [sendButton.heightAnchor constraintEqualToConstant:36]
    ]];
}

- (void)closeChat {
    [self dismissViewControllerAnimated:YES completion:nil];
}

@end

// MARK: - Details View Controller

@implementation CelraDetailsViewController

- (instancetype)initWithAdvisor:(NSDictionary *)advisor {
    self = [super init];
    if (self) {
        _advisor = advisor;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupDetailsUI];
}

- (void)setupDetailsUI {
    self.view.backgroundColor = CELRA_PRIMARY_BG;
    self.navigationItem.title = @"Advisor Details";
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"Close" style:UIBarButtonItemStylePlain target:self action:@selector(closeDetails)];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"Chat" style:UIBarButtonItemStylePlain target:self action:@selector(startChat)];

    // Scroll view for magazine-style layout
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:scrollView];

    UIView *contentView = [[UIView alloc] init];
    contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [scrollView addSubview:contentView];

    // Large character image
    UIImageView *characterImageView = [[UIImageView alloc] init];
    characterImageView.image = [UIImage imageNamed:self.advisor[@"id"]];
    characterImageView.contentMode = UIViewContentModeScaleAspectFit;
    characterImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:characterImageView];

    // Name overlay on image
    UIView *nameOverlay = [[UIView alloc] init];
    nameOverlay.backgroundColor = [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.7];
    nameOverlay.layer.cornerRadius = 12;
    nameOverlay.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:nameOverlay];

    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.text = self.advisor[@"name"];
    nameLabel.font = [UIFont boldSystemFontOfSize:24];
    nameLabel.textColor = [UIColor whiteColor];
    nameLabel.textAlignment = NSTextAlignmentCenter;
    nameLabel.numberOfLines = 2;
    nameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [nameOverlay addSubview:nameLabel];

    UILabel *englishNameLabel = [[UILabel alloc] init];
    englishNameLabel.text = self.advisor[@"english"];
    englishNameLabel.font = [UIFont systemFontOfSize:20];
    englishNameLabel.textColor = CELRA_ACCENT;
    englishNameLabel.textAlignment = NSTextAlignmentCenter;
    englishNameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [nameOverlay addSubview:englishNameLabel];

    // Description section
    UILabel *descriptionTitle = [[UILabel alloc] init];
    descriptionTitle.text = @"EXPERTISE";
    descriptionTitle.font = [UIFont boldSystemFontOfSize:16];
    descriptionTitle.textColor = CELRA_ACCENT;
    descriptionTitle.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:descriptionTitle];

    UILabel *descriptionLabel = [[UILabel alloc] init];
    descriptionLabel.text = self.advisor[@"desc"];
    descriptionLabel.font = [UIFont systemFontOfSize:18];
    descriptionLabel.textColor = [UIColor whiteColor];
    descriptionLabel.numberOfLines = 0;
    descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [contentView addSubview:descriptionLabel];

    // Chat button
    UIButton *chatButton = [[UIButton alloc] init];
    [chatButton setTitle:@"Start Conversation" forState:UIControlStateNormal];
    chatButton.backgroundColor = CELRA_ACCENT;
    [chatButton setTitleColor:CELRA_PRIMARY_BG forState:UIControlStateNormal];
    chatButton.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    chatButton.layer.cornerRadius = 12;
    chatButton.translatesAutoresizingMaskIntoConstraints = NO;
    [chatButton addTarget:self action:@selector(startChat) forControlEvents:UIControlEventTouchUpInside];
    [contentView addSubview:chatButton];

    // Setup constraints
    [NSLayoutConstraint activateConstraints:@[
        // Scroll view
        [scrollView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [scrollView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [scrollView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [scrollView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor],

        // Content view
        [contentView.topAnchor constraintEqualToAnchor:scrollView.topAnchor],
        [contentView.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor],
        [contentView.trailingAnchor constraintEqualToAnchor:scrollView.trailingAnchor],
        [contentView.bottomAnchor constraintEqualToAnchor:scrollView.bottomAnchor],
        [contentView.widthAnchor constraintEqualToAnchor:scrollView.widthAnchor],

        // Character image
        [characterImageView.topAnchor constraintEqualToAnchor:contentView.topAnchor constant:20],
        [characterImageView.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:20],
        [characterImageView.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-20],
        [characterImageView.heightAnchor constraintEqualToConstant:400],

        // Name overlay
        [nameOverlay.leadingAnchor constraintEqualToAnchor:characterImageView.leadingAnchor constant:20],
        [nameOverlay.trailingAnchor constraintEqualToAnchor:characterImageView.trailingAnchor constant:-20],
        [nameOverlay.bottomAnchor constraintEqualToAnchor:characterImageView.bottomAnchor constant:-20],

        // Name label
        [nameLabel.topAnchor constraintEqualToAnchor:nameOverlay.topAnchor constant:16],
        [nameLabel.leadingAnchor constraintEqualToAnchor:nameOverlay.leadingAnchor constant:16],
        [nameLabel.trailingAnchor constraintEqualToAnchor:nameOverlay.trailingAnchor constant:-16],

        // English name label
        [englishNameLabel.topAnchor constraintEqualToAnchor:nameLabel.bottomAnchor constant:8],
        [englishNameLabel.leadingAnchor constraintEqualToAnchor:nameOverlay.leadingAnchor constant:16],
        [englishNameLabel.trailingAnchor constraintEqualToAnchor:nameOverlay.trailingAnchor constant:-16],
        [englishNameLabel.bottomAnchor constraintEqualToAnchor:nameOverlay.bottomAnchor constant:-16],

        // Description title
        [descriptionTitle.topAnchor constraintEqualToAnchor:characterImageView.bottomAnchor constant:30],
        [descriptionTitle.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:20],
        [descriptionTitle.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-20],

        // Description
        [descriptionLabel.topAnchor constraintEqualToAnchor:descriptionTitle.bottomAnchor constant:12],
        [descriptionLabel.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:20],
        [descriptionLabel.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-20],

        // Chat button
        [chatButton.topAnchor constraintEqualToAnchor:descriptionLabel.bottomAnchor constant:40],
        [chatButton.leadingAnchor constraintEqualToAnchor:contentView.leadingAnchor constant:20],
        [chatButton.trailingAnchor constraintEqualToAnchor:contentView.trailingAnchor constant:-20],
        [chatButton.heightAnchor constraintEqualToConstant:50],
        [chatButton.bottomAnchor constraintEqualToAnchor:contentView.bottomAnchor constant:-20]
    ]];
}

- (void)closeDetails {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)startChat {
    CelraChatViewController *chatVC = [[CelraChatViewController alloc] initWithAdvisor:self.advisor];
    [self.navigationController pushViewController:chatVC animated:YES];
}

@end
