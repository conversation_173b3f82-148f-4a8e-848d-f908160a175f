//
//  AppDelegate.m
//  Celra
//
//  Created by asde007 on 2025/8/4.
//

#import "AppDelegate.h"
#import "MainTabBarController.h"

@interface AppDelegate ()

@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // For iOS 12 compatibility - create window if Scene Delegate is not available
    if (@available(iOS 13.0, *)) {
        // iOS 13+ uses Scene Delegate
    } else {
        // iOS 12 fallback
        self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];

        MainTabBarController *mainTabBarController = [[MainTabBarController alloc] init];
        self.window.rootViewController = mainTabBarController;

        [self.window makeKeyAndVisible];
    }

    return YES;
}


#pragma mark - UISceneSession lifecycle


- (UISceneConfiguration *)application:(UIApplication *)application configurationForConnectingSceneSession:(UISceneSession *)connectingSceneSession options:(UISceneConnectionOptions *)options {
    // Called when a new scene session is being created.
    // Use this method to select a configuration to create the new scene with.
    return [[UISceneConfiguration alloc] initWithName:@"Default Configuration" sessionRole:connectingSceneSession.role];
}


- (void)application:(UIApplication *)application didDiscardSceneSessions:(NSSet<UISceneSession *> *)sceneSessions {
    // Called when the user discards a scene session.
    // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
    // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
}


@end
