//
//  SoundManager.m
//  Celra
//
//  Sound effects manager implementation
//

#import "SoundManager.h"
#import <AVFoundation/AVFoundation.h>
#import <AudioToolbox/AudioToolbox.h>

@interface SoundManager ()

@property (nonatomic, strong) AVAudioPlayer *knockPlayer;
@property (nonatomic, strong) AVAudioPlayer *doorOpenPlayer;
@property (nonatomic, strong) AVAudioPlayer *recordingStartPlayer;
@property (nonatomic, strong) AVAudioPlayer *recordingStopPlayer;

@end

@implementation SoundManager

+ (instancetype)sharedManager {
    static SoundManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[SoundManager alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupSounds];
    }
    return self;
}

- (void)setupSounds {
    // In a real app, you would load actual sound files
    // For now, we'll use system sounds or create simple tones
    
    // Setup audio session
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    NSError *error;
    [audioSession setCategory:AVAudioSessionCategoryAmbient error:&error];
    [audioSession setActive:YES error:&error];
}

- (void)playKnockSound {
    // Play knock sound effect
    // In a real implementation, you would load a knock.wav file
    AudioServicesPlaySystemSound(1104); // System sound for knock-like effect
}

- (void)playDoorOpenSound {
    // Play door opening sound effect
    // In a real implementation, you would load a door_open.wav file
    AudioServicesPlaySystemSound(1108); // System sound for door-like effect
}

- (void)playRecordingStartSound {
    // Play recording start sound
    AudioServicesPlaySystemSound(1117); // System sound for start recording
}

- (void)playRecordingStopSound {
    // Play recording stop sound
    AudioServicesPlaySystemSound(1118); // System sound for stop recording
}

- (AVAudioPlayer *)createPlayerWithSoundName:(NSString *)soundName {
    NSString *soundPath = [[NSBundle mainBundle] pathForResource:soundName ofType:@"wav"];
    if (soundPath) {
        NSURL *soundURL = [NSURL fileURLWithPath:soundPath];
        NSError *error;
        AVAudioPlayer *player = [[AVAudioPlayer alloc] initWithContentsOfURL:soundURL error:&error];
        if (!error) {
            [player prepareToPlay];
            return player;
        }
    }
    return nil;
}

@end
