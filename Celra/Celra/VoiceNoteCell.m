//
//  VoiceNoteCell.m
//  Celra
//
//  Voice note table view cell implementation
//

#import "VoiceNoteCell.h"
#import "CelraDesignSystem.h"

@interface VoiceNoteCell ()

@property (nonatomic, strong) UIView *noteCardView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *textLabel;
@property (nonatomic, strong) UILabel *timestampLabel;
@property (nonatomic, strong) UILabel *durationLabel;
@property (nonatomic, strong) UIView *microphoneIcon;

@end

@implementation VoiceNoteCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        [self setupConstraints];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor clearColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // Note card view
    self.noteCardView = [[UIView alloc] init];
    self.noteCardView.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    self.noteCardView.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.noteCardView.layer.shadowColor = [CelraDesignSystem shadowColor].CGColor;
    self.noteCardView.layer.shadowOffset = CGSizeMake(0, 2);
    self.noteCardView.layer.shadowRadius = 4;
    self.noteCardView.layer.shadowOpacity = 1.0;
    self.noteCardView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.noteCardView];
    
    // Microphone icon
    self.microphoneIcon = [[UIView alloc] init];
    self.microphoneIcon.backgroundColor = [CelraDesignSystem accentColor];
    self.microphoneIcon.layer.cornerRadius = 15;
    self.microphoneIcon.translatesAutoresizingMaskIntoConstraints = NO;
    [self.noteCardView addSubview:self.microphoneIcon];
    
    // Add microphone symbol
    UILabel *micLabel = [[UILabel alloc] init];
    micLabel.text = @"🎤";
    micLabel.font = [UIFont systemFontOfSize:16];
    micLabel.textAlignment = NSTextAlignmentCenter;
    micLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.microphoneIcon addSubview:micLabel];
    
    [NSLayoutConstraint activateConstraints:@[
        [micLabel.centerXAnchor constraintEqualToAnchor:self.microphoneIcon.centerXAnchor],
        [micLabel.centerYAnchor constraintEqualToAnchor:self.microphoneIcon.centerYAnchor]
    ]];
    
    // Title label
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [CelraDesignSystem headlineFont];
    self.titleLabel.textColor = [CelraDesignSystem textPrimaryColor];
    self.titleLabel.numberOfLines = 1;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.noteCardView addSubview:self.titleLabel];
    
    // Text label
    self.textLabel = [[UILabel alloc] init];
    self.textLabel.font = [CelraDesignSystem bodyFont];
    self.textLabel.textColor = [CelraDesignSystem secondaryBackgroundColor];
    self.textLabel.numberOfLines = 2;
    self.textLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.noteCardView addSubview:self.textLabel];
    
    // Timestamp label
    self.timestampLabel = [[UILabel alloc] init];
    self.timestampLabel.font = [CelraDesignSystem captionFont];
    self.timestampLabel.textColor = [CelraDesignSystem secondaryBackgroundColor];
    self.timestampLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.noteCardView addSubview:self.timestampLabel];
    
    // Duration label
    self.durationLabel = [[UILabel alloc] init];
    self.durationLabel.font = [CelraDesignSystem captionFont];
    self.durationLabel.textColor = [CelraDesignSystem accentColor];
    self.durationLabel.textAlignment = NSTextAlignmentRight;
    self.durationLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.noteCardView addSubview:self.durationLabel];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Note card view
        [self.noteCardView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor 
                                                    constant:[CelraDesignSystem spacingS]],
        [self.noteCardView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor 
                                                        constant:[CelraDesignSystem spacingM]],
        [self.noteCardView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor 
                                                         constant:-[CelraDesignSystem spacingM]],
        [self.noteCardView.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor 
                                                       constant:-[CelraDesignSystem spacingS]],
        
        // Microphone icon
        [self.microphoneIcon.leadingAnchor constraintEqualToAnchor:self.noteCardView.leadingAnchor 
                                                          constant:[CelraDesignSystem spacingM]],
        [self.microphoneIcon.topAnchor constraintEqualToAnchor:self.noteCardView.topAnchor 
                                                      constant:[CelraDesignSystem spacingM]],
        [self.microphoneIcon.widthAnchor constraintEqualToConstant:30],
        [self.microphoneIcon.heightAnchor constraintEqualToConstant:30],
        
        // Title label
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.microphoneIcon.trailingAnchor 
                                                      constant:[CelraDesignSystem spacingM]],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.noteCardView.trailingAnchor 
                                                       constant:-[CelraDesignSystem spacingM]],
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.noteCardView.topAnchor 
                                                  constant:[CelraDesignSystem spacingM]],
        
        // Text label
        [self.textLabel.leadingAnchor constraintEqualToAnchor:self.titleLabel.leadingAnchor],
        [self.textLabel.trailingAnchor constraintEqualToAnchor:self.titleLabel.trailingAnchor],
        [self.textLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor 
                                                 constant:[CelraDesignSystem spacingXS]],
        
        // Timestamp label
        [self.timestampLabel.leadingAnchor constraintEqualToAnchor:self.titleLabel.leadingAnchor],
        [self.timestampLabel.topAnchor constraintEqualToAnchor:self.textLabel.bottomAnchor 
                                                      constant:[CelraDesignSystem spacingS]],
        [self.timestampLabel.bottomAnchor constraintEqualToAnchor:self.noteCardView.bottomAnchor 
                                                         constant:-[CelraDesignSystem spacingM]],
        
        // Duration label
        [self.durationLabel.trailingAnchor constraintEqualToAnchor:self.titleLabel.trailingAnchor],
        [self.durationLabel.centerYAnchor constraintEqualToAnchor:self.timestampLabel.centerYAnchor]
    ]];
}

- (void)configureWithVoiceNote:(VoiceNote *)note {
    self.titleLabel.text = note.title;
    self.textLabel.text = note.text.length > 0 ? note.text : @"No transcription available";
    
    // Format timestamp
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"MMM dd, HH:mm";
    self.timestampLabel.text = [formatter stringFromDate:note.timestamp];
    
    // Format duration
    NSInteger minutes = (NSInteger)(note.duration / 60);
    NSInteger seconds = (NSInteger)note.duration % 60;
    self.durationLabel.text = [NSString stringWithFormat:@"%ld:%02ld", (long)minutes, (long)seconds];
}

@end
