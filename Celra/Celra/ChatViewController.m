//
//  ChatViewController.m
//  Celra
//
//  Chat view controller implementation
//

#import "ChatViewController.h"
#import "CelraDesignSystem.h"
#import "ChatMessageCell.h"
#import "ChatMessage.h"

@interface ChatViewController () <UITableViewDataSource, UITableViewDelegate, UITextViewDelegate>

@property (nonatomic, strong) AICharacter *character;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *inputContainerView;
@property (nonatomic, strong) UITextView *inputTextView;
@property (nonatomic, strong) UIButton *sendButton;
@property (nonatomic, strong) NSMutableArray<ChatMessage *> *messages;
@property (nonatomic, strong) UIView *characterHeaderView;
@property (nonatomic, strong) UIImageView *characterAvatarView;
@property (nonatomic, strong) UILabel *characterNameLabel;
@property (nonatomic, strong) UILabel *characterDescriptionLabel;

@end

@implementation ChatViewController

- (instancetype)initWithCharacter:(AICharacter *)character {
    self = [super init];
    if (self) {
        _character = character;
        _messages = [NSMutableArray array];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupConstraints];
    [self addInitialMessage];
    [self setupKeyboardObservers];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupUI {
    self.view.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
    self.navigationItem.title = self.character.englishName;
    
    // Character header view
    self.characterHeaderView = [[UIView alloc] init];
    self.characterHeaderView.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    self.characterHeaderView.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.characterHeaderView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.characterHeaderView];
    
    // Character avatar
    self.characterAvatarView = [[UIImageView alloc] init];
    self.characterAvatarView.backgroundColor = self.character.doorColor;
    self.characterAvatarView.layer.cornerRadius = 30;
    self.characterAvatarView.layer.borderWidth = 2;
    self.characterAvatarView.layer.borderColor = self.character.accentColor.CGColor;
    self.characterAvatarView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.characterHeaderView addSubview:self.characterAvatarView];
    
    // Character name
    self.characterNameLabel = [[UILabel alloc] init];
    self.characterNameLabel.text = self.character.chineseName;
    self.characterNameLabel.font = [CelraDesignSystem headlineFont];
    self.characterNameLabel.textColor = [CelraDesignSystem textPrimaryColor];
    self.characterNameLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.characterHeaderView addSubview:self.characterNameLabel];
    
    // Character description
    self.characterDescriptionLabel = [[UILabel alloc] init];
    self.characterDescriptionLabel.text = self.character.shortDescription;
    self.characterDescriptionLabel.font = [CelraDesignSystem captionFont];
    self.characterDescriptionLabel.textColor = [CelraDesignSystem secondaryBackgroundColor];
    self.characterDescriptionLabel.numberOfLines = 2;
    self.characterDescriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.characterHeaderView addSubview:self.characterDescriptionLabel];
    
    // Table view for messages
    self.tableView = [[UITableView alloc] init];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.tableView registerClass:[ChatMessageCell class] forCellReuseIdentifier:@"ChatMessageCell"];
    [self.view addSubview:self.tableView];
    
    // Input container
    self.inputContainerView = [[UIView alloc] init];
    self.inputContainerView.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    self.inputContainerView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.inputContainerView];
    
    // Input text view
    self.inputTextView = [[UITextView alloc] init];
    self.inputTextView.delegate = self;
    self.inputTextView.font = [CelraDesignSystem bodyFont];
    self.inputTextView.textColor = [CelraDesignSystem textPrimaryColor];
    self.inputTextView.backgroundColor = [CelraDesignSystem primaryBackgroundColor];
    self.inputTextView.layer.cornerRadius = [CelraDesignSystem cornerRadiusS];
    self.inputTextView.layer.borderWidth = 1;
    self.inputTextView.layer.borderColor = [CelraDesignSystem secondaryBackgroundColor].CGColor;
    self.inputTextView.textContainerInset = UIEdgeInsetsMake(8, 8, 8, 8);
    self.inputTextView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.inputContainerView addSubview:self.inputTextView];
    
    // Send button
    self.sendButton = [[UIButton alloc] init];
    [self.sendButton setTitle:@"Send" forState:UIControlStateNormal];
    [self.sendButton setTitleColor:[CelraDesignSystem primaryBackgroundColor] forState:UIControlStateNormal];
    self.sendButton.titleLabel.font = [CelraDesignSystem buttonFont];
    self.sendButton.backgroundColor = [CelraDesignSystem accentColor];
    self.sendButton.layer.cornerRadius = [CelraDesignSystem cornerRadiusS];
    self.sendButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.sendButton addTarget:self action:@selector(sendButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.inputContainerView addSubview:self.sendButton];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Character header
        [self.characterHeaderView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor 
                                                           constant:[CelraDesignSystem spacingM]],
        [self.characterHeaderView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor 
                                                               constant:[CelraDesignSystem spacingM]],
        [self.characterHeaderView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor 
                                                                constant:-[CelraDesignSystem spacingM]],
        [self.characterHeaderView.heightAnchor constraintEqualToConstant:80],
        
        // Character avatar
        [self.characterAvatarView.leadingAnchor constraintEqualToAnchor:self.characterHeaderView.leadingAnchor 
                                                               constant:[CelraDesignSystem spacingM]],
        [self.characterAvatarView.centerYAnchor constraintEqualToAnchor:self.characterHeaderView.centerYAnchor],
        [self.characterAvatarView.widthAnchor constraintEqualToConstant:60],
        [self.characterAvatarView.heightAnchor constraintEqualToConstant:60],
        
        // Character name
        [self.characterNameLabel.leadingAnchor constraintEqualToAnchor:self.characterAvatarView.trailingAnchor 
                                                              constant:[CelraDesignSystem spacingM]],
        [self.characterNameLabel.trailingAnchor constraintEqualToAnchor:self.characterHeaderView.trailingAnchor 
                                                               constant:-[CelraDesignSystem spacingM]],
        [self.characterNameLabel.topAnchor constraintEqualToAnchor:self.characterHeaderView.topAnchor 
                                                          constant:[CelraDesignSystem spacingM]],
        
        // Character description
        [self.characterDescriptionLabel.leadingAnchor constraintEqualToAnchor:self.characterNameLabel.leadingAnchor],
        [self.characterDescriptionLabel.trailingAnchor constraintEqualToAnchor:self.characterNameLabel.trailingAnchor],
        [self.characterDescriptionLabel.topAnchor constraintEqualToAnchor:self.characterNameLabel.bottomAnchor 
                                                                 constant:[CelraDesignSystem spacingXS]],
        
        // Input container
        [self.inputContainerView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.inputContainerView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.inputContainerView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor],
        [self.inputContainerView.heightAnchor constraintGreaterThanOrEqualToConstant:60],
        
        // Input text view
        [self.inputTextView.leadingAnchor constraintEqualToAnchor:self.inputContainerView.leadingAnchor 
                                                         constant:[CelraDesignSystem spacingM]],
        [self.inputTextView.topAnchor constraintEqualToAnchor:self.inputContainerView.topAnchor 
                                                     constant:[CelraDesignSystem spacingS]],
        [self.inputTextView.bottomAnchor constraintEqualToAnchor:self.inputContainerView.bottomAnchor 
                                                        constant:-[CelraDesignSystem spacingS]],
        [self.inputTextView.heightAnchor constraintGreaterThanOrEqualToConstant:44],
        
        // Send button
        [self.sendButton.leadingAnchor constraintEqualToAnchor:self.inputTextView.trailingAnchor 
                                                      constant:[CelraDesignSystem spacingS]],
        [self.sendButton.trailingAnchor constraintEqualToAnchor:self.inputContainerView.trailingAnchor 
                                                       constant:-[CelraDesignSystem spacingM]],
        [self.sendButton.bottomAnchor constraintEqualToAnchor:self.inputTextView.bottomAnchor],
        [self.sendButton.widthAnchor constraintEqualToConstant:60],
        [self.sendButton.heightAnchor constraintEqualToConstant:44],
        
        // Table view
        [self.tableView.topAnchor constraintEqualToAnchor:self.characterHeaderView.bottomAnchor 
                                                 constant:[CelraDesignSystem spacingM]],
        [self.tableView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.tableView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.tableView.bottomAnchor constraintEqualToAnchor:self.inputContainerView.topAnchor]
    ]];
}

- (void)addInitialMessage {
    ChatMessage *welcomeMessage = [[ChatMessage alloc] init];
    welcomeMessage.text = [NSString stringWithFormat:@"Hello! I'm %@. %@", self.character.englishName, self.character.longDescription];
    welcomeMessage.isFromUser = NO;
    welcomeMessage.timestamp = [NSDate date];
    [self.messages addObject:welcomeMessage];
    [self.tableView reloadData];
}

- (void)sendButtonTapped:(UIButton *)sender {
    NSString *messageText = [self.inputTextView.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (messageText.length > 0) {
        // Add user message
        ChatMessage *userMessage = [[ChatMessage alloc] init];
        userMessage.text = messageText;
        userMessage.isFromUser = YES;
        userMessage.timestamp = [NSDate date];
        [self.messages addObject:userMessage];
        
        // Clear input
        self.inputTextView.text = @"";
        
        // Reload table and scroll to bottom
        [self.tableView reloadData];
        [self scrollToBottom];
        
        // Simulate AI response (in a real app, this would be an API call)
        [self simulateAIResponse];
    }
}

- (void)simulateAIResponse {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        ChatMessage *aiMessage = [[ChatMessage alloc] init];
        aiMessage.text = @"That's a great question about storyboarding! Let me help you with that...";
        aiMessage.isFromUser = NO;
        aiMessage.timestamp = [NSDate date];
        [self.messages addObject:aiMessage];
        
        [self.tableView reloadData];
        [self scrollToBottom];
    });
}

- (void)scrollToBottom {
    if (self.messages.count > 0) {
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:self.messages.count - 1 inSection:0];
        [self.tableView scrollToRowAtIndexPath:indexPath atScrollPosition:UITableViewScrollPositionBottom animated:YES];
    }
}

- (void)setupKeyboardObservers {
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(keyboardWillShow:) 
                                                 name:UIKeyboardWillShowNotification 
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(keyboardWillHide:) 
                                                 name:UIKeyboardWillHideNotification 
                                               object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    // Handle keyboard appearance
}

- (void)keyboardWillHide:(NSNotification *)notification {
    // Handle keyboard dismissal
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.messages.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ChatMessageCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ChatMessageCell" forIndexPath:indexPath];
    ChatMessage *message = self.messages[indexPath.row];
    [cell configureWithMessage:message character:self.character];
    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}

@end
