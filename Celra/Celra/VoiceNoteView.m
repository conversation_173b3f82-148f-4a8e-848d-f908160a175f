//
//  VoiceNoteView.m
//  Celra
//
//  Voice note recording view implementation
//

#import "VoiceNoteView.h"
#import "CelraDesignSystem.h"
#import "SoundManager.h"
#import <AVFoundation/AVFoundation.h>
#import <Speech/Speech.h>

@interface VoiceNoteView () <AVAudioRecorderDelegate>

@property (nonatomic, strong) UIButton *microphoneButton;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UIView *waveformView;
@property (nonatomic, strong) AVAudioRecorder *audioRecorder;
@property (nonatomic, strong) SFSpeechRecognizer *speechRecognizer;
@property (nonatomic, strong) SFSpeechAudioBufferRecognitionRequest *recognitionRequest;
@property (nonatomic, strong) SFSpeechRecognitionTask *recognitionTask;
@property (nonatomic, strong) AVAudioEngine *audioEngine;
@property (nonatomic, assign) BOOL isRecording;
@property (nonatomic, strong) NSDate *recordingStartTime;
@property (nonatomic, strong) NSMutableString *transcribedText;

@end

@implementation VoiceNoteView

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupUI];
        [self setupConstraints];
        [self setupAudio];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [CelraDesignSystem cardBackgroundColor];
    self.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.layer.shadowColor = [CelraDesignSystem shadowColor].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 2);
    self.layer.shadowRadius = 8;
    self.layer.shadowOpacity = 1.0;
    
    // Microphone button
    self.microphoneButton = [[UIButton alloc] init];
    [self.microphoneButton setTitle:@"🎤" forState:UIControlStateNormal];
    self.microphoneButton.titleLabel.font = [UIFont systemFontOfSize:24];
    self.microphoneButton.backgroundColor = [CelraDesignSystem accentColor];
    self.microphoneButton.layer.cornerRadius = 25;
    self.microphoneButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.microphoneButton addTarget:self action:@selector(microphoneButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.microphoneButton];
    
    // Status label
    self.statusLabel = [[UILabel alloc] init];
    self.statusLabel.text = @"Tap to record";
    self.statusLabel.font = [CelraDesignSystem captionFont];
    self.statusLabel.textColor = [CelraDesignSystem textPrimaryColor];
    self.statusLabel.textAlignment = NSTextAlignmentCenter;
    self.statusLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.statusLabel];
    
    // Waveform view (visual feedback during recording)
    self.waveformView = [[UIView alloc] init];
    self.waveformView.backgroundColor = [CelraDesignSystem accentColor];
    self.waveformView.layer.cornerRadius = 2;
    self.waveformView.alpha = 0;
    self.waveformView.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.waveformView];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // Microphone button
        [self.microphoneButton.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.microphoneButton.topAnchor constraintEqualToAnchor:self.topAnchor 
                                                        constant:[CelraDesignSystem spacingM]],
        [self.microphoneButton.widthAnchor constraintEqualToConstant:50],
        [self.microphoneButton.heightAnchor constraintEqualToConstant:50],
        
        // Status label
        [self.statusLabel.topAnchor constraintEqualToAnchor:self.microphoneButton.bottomAnchor 
                                                   constant:[CelraDesignSystem spacingS]],
        [self.statusLabel.leadingAnchor constraintEqualToAnchor:self.leadingAnchor 
                                                       constant:[CelraDesignSystem spacingS]],
        [self.statusLabel.trailingAnchor constraintEqualToAnchor:self.trailingAnchor 
                                                        constant:-[CelraDesignSystem spacingS]],
        
        // Waveform view
        [self.waveformView.topAnchor constraintEqualToAnchor:self.statusLabel.bottomAnchor 
                                                    constant:[CelraDesignSystem spacingS]],
        [self.waveformView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
        [self.waveformView.widthAnchor constraintEqualToConstant:60],
        [self.waveformView.heightAnchor constraintEqualToConstant:4]
    ]];
}

- (void)setupAudio {
    self.speechRecognizer = [[SFSpeechRecognizer alloc] initWithLocale:[NSLocale localeWithLocaleIdentifier:@"en-US"]];
    self.audioEngine = [[AVAudioEngine alloc] init];
    self.transcribedText = [NSMutableString string];
}

- (void)microphoneButtonTapped:(UIButton *)sender {
    if (self.isRecording) {
        [self stopRecording];
    } else {
        [self startRecording];
    }
}

- (void)startRecording {
    // Request permissions
    [self requestPermissions:^(BOOL granted) {
        if (granted) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self beginRecording];
            });
        } else {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self showPermissionAlert];
            });
        }
    }];
}

- (void)requestPermissions:(void(^)(BOOL granted))completion {
    [AVAudioSession.sharedInstance requestRecordPermission:^(BOOL granted) {
        if (granted) {
            [SFSpeechRecognizer requestAuthorization:^(SFSpeechRecognizerAuthorizationStatus status) {
                completion(status == SFSpeechRecognizerAuthorizationStatusAuthorized);
            }];
        } else {
            completion(NO);
        }
    }];
}

- (void)beginRecording {
    self.isRecording = YES;
    self.recordingStartTime = [NSDate date];
    [self.transcribedText setString:@""];

    // Play recording start sound
    [[SoundManager sharedManager] playRecordingStartSound];

    // Update UI
    [self.microphoneButton setTitle:@"⏹" forState:UIControlStateNormal];
    self.microphoneButton.backgroundColor = [UIColor redColor];
    self.statusLabel.text = @"Recording...";

    // Start waveform animation
    [self startWaveformAnimation];

    // Setup audio session
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    NSError *error;
    [audioSession setCategory:AVAudioSessionCategoryRecord mode:AVAudioSessionModeMeasurement options:AVAudioSessionCategoryOptionDuckOthers error:&error];
    [audioSession setActive:YES withOptions:AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation error:&error];

    // Start speech recognition
    [self startSpeechRecognition];
}

- (void)startSpeechRecognition {
    if (self.recognitionTask) {
        [self.recognitionTask cancel];
        self.recognitionTask = nil;
    }
    
    self.recognitionRequest = [[SFSpeechAudioBufferRecognitionRequest alloc] init];
    
    AVAudioInputNode *inputNode = self.audioEngine.inputNode;
    self.recognitionRequest.shouldReportPartialResults = YES;
    
    self.recognitionTask = [self.speechRecognizer recognitionTaskWithRequest:self.recognitionRequest resultHandler:^(SFSpeechRecognitionResult * _Nullable result, NSError * _Nullable error) {
        if (result) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.transcribedText setString:result.bestTranscription.formattedString];
            });
        }
        
        if (error || result.isFinal) {
            [self.audioEngine stop];
            [inputNode removeTapOnBus:0];
            self.recognitionRequest = nil;
            self.recognitionTask = nil;
        }
    }];
    
    AVAudioFormat *recordingFormat = [inputNode outputFormatForBus:0];
    [inputNode installTapOnBus:0 bufferSize:1024 format:recordingFormat block:^(AVAudioPCMBuffer * _Nonnull buffer, AVAudioTime * _Nonnull when) {
        [self.recognitionRequest appendAudioPCMBuffer:buffer];
    }];
    
    [self.audioEngine prepare];
    NSError *error;
    [self.audioEngine startAndReturnError:&error];
}

- (void)stopRecording {
    self.isRecording = NO;

    // Play recording stop sound
    [[SoundManager sharedManager] playRecordingStopSound];

    // Stop audio engine and recognition
    [self.audioEngine stop];
    [self.recognitionRequest endAudio];

    // Update UI
    [self.microphoneButton setTitle:@"🎤" forState:UIControlStateNormal];
    self.microphoneButton.backgroundColor = [CelraDesignSystem accentColor];
    self.statusLabel.text = @"Processing...";

    // Stop waveform animation
    [self stopWaveformAnimation];

    // Create voice note
    [self createVoiceNote];
}

- (void)createVoiceNote {
    VoiceNote *note = [[VoiceNote alloc] init];
    note.text = [self.transcribedText copy];
    note.duration = [[NSDate date] timeIntervalSinceDate:self.recordingStartTime];
    note.title = [self generateTitleFromText:note.text];
    
    // Update status
    self.statusLabel.text = @"Note saved!";
    
    // Reset after delay
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.statusLabel.text = @"Tap to record";
    });
    
    // Notify delegate
    if ([self.delegate respondsToSelector:@selector(voiceNoteView:didCreateNote:)]) {
        [self.delegate voiceNoteView:self didCreateNote:note];
    }
}

- (NSString *)generateTitleFromText:(NSString *)text {
    if (text.length == 0) {
        return @"Voice Note";
    }
    
    NSArray *words = [text componentsSeparatedByString:@" "];
    NSInteger maxWords = MIN(5, words.count);
    NSArray *titleWords = [words subarrayWithRange:NSMakeRange(0, maxWords)];
    NSString *title = [titleWords componentsJoinedByString:@" "];
    
    if (words.count > maxWords) {
        title = [title stringByAppendingString:@"..."];
    }
    
    return title;
}

- (void)startWaveformAnimation {
    self.waveformView.alpha = 1.0;
    [UIView animateWithDuration:0.5 delay:0 options:UIViewAnimationOptionRepeat | UIViewAnimationOptionAutoreverse animations:^{
        self.waveformView.transform = CGAffineTransformMakeScaleX(2.0, 1.0);
    } completion:nil];
}

- (void)stopWaveformAnimation {
    [self.waveformView.layer removeAllAnimations];
    [UIView animateWithDuration:0.3 animations:^{
        self.waveformView.alpha = 0;
        self.waveformView.transform = CGAffineTransformIdentity;
    }];
}

- (void)showPermissionAlert {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Permission Required" 
                                                                   message:@"Celra needs microphone and speech recognition permissions to create voice notes." 
                                                            preferredStyle:UIAlertControllerStyleAlert];
    
    UIAlertAction *settingsAction = [UIAlertAction actionWithTitle:@"Settings" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:nil];
    }];
    
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil];
    
    [alert addAction:settingsAction];
    [alert addAction:cancelAction];
    
    UIViewController *presentingVC = [self findViewController];
    if (presentingVC) {
        [presentingVC presentViewController:alert animated:YES completion:nil];
    }
}

- (UIViewController *)findViewController {
    UIResponder *responder = self;
    while (responder) {
        if ([responder isKindOfClass:[UIViewController class]]) {
            return (UIViewController *)responder;
        }
        responder = [responder nextResponder];
    }
    return nil;
}

@end
