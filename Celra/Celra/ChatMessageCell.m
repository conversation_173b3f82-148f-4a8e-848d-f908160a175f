//
//  ChatMessageCell.m
//  Celra
//
//  Chat message table view cell implementation
//

#import "ChatMessageCell.h"
#import "CelraDesignSystem.h"

@interface ChatMessageCell ()

@property (nonatomic, strong) UIView *bubbleView;
@property (nonatomic, strong) UILabel *messageLabel;
@property (nonatomic, strong) UILabel *timestampLabel;
@property (nonatomic, strong) UIImageView *avatarImageView;
@property (nonatomic, strong) NSLayoutConstraint *bubbleLeadingConstraint;
@property (nonatomic, strong) NSLayoutConstraint *bubbleTrailingConstraint;

@end

@implementation ChatMessageCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        [self setupConstraints];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor clearColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // Avatar image view
    self.avatarImageView = [[UIImageView alloc] init];
    self.avatarImageView.layer.cornerRadius = 20;
    self.avatarImageView.clipsToBounds = YES;
    self.avatarImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.avatarImageView];
    
    // Bubble view
    self.bubbleView = [[UIView alloc] init];
    self.bubbleView.layer.cornerRadius = [CelraDesignSystem cornerRadiusM];
    self.bubbleView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.bubbleView];
    
    // Message label
    self.messageLabel = [[UILabel alloc] init];
    self.messageLabel.font = [CelraDesignSystem bodyFont];
    self.messageLabel.numberOfLines = 0;
    self.messageLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.bubbleView addSubview:self.messageLabel];
    
    // Timestamp label
    self.timestampLabel = [[UILabel alloc] init];
    self.timestampLabel.font = [CelraDesignSystem captionFont];
    self.timestampLabel.textColor = [CelraDesignSystem secondaryBackgroundColor];
    self.timestampLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:self.timestampLabel];
}

- (void)setupConstraints {
    // Avatar constraints
    [NSLayoutConstraint activateConstraints:@[
        [self.avatarImageView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor 
                                                       constant:[CelraDesignSystem spacingS]],
        [self.avatarImageView.widthAnchor constraintEqualToConstant:40],
        [self.avatarImageView.heightAnchor constraintEqualToConstant:40]
    ]];
    
    // Bubble constraints (will be updated based on message type)
    self.bubbleLeadingConstraint = [self.bubbleView.leadingAnchor constraintEqualToAnchor:self.avatarImageView.trailingAnchor 
                                                                                 constant:[CelraDesignSystem spacingS]];
    self.bubbleTrailingConstraint = [self.bubbleView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor 
                                                                                   constant:-[CelraDesignSystem spacingM]];
    
    [NSLayoutConstraint activateConstraints:@[
        [self.bubbleView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor 
                                                  constant:[CelraDesignSystem spacingS]],
        [self.bubbleView.widthAnchor constraintLessThanOrEqualToConstant:280]
    ]];
    
    // Message label constraints
    [NSLayoutConstraint activateConstraints:@[
        [self.messageLabel.topAnchor constraintEqualToAnchor:self.bubbleView.topAnchor 
                                                    constant:[CelraDesignSystem spacingS]],
        [self.messageLabel.leadingAnchor constraintEqualToAnchor:self.bubbleView.leadingAnchor 
                                                        constant:[CelraDesignSystem spacingM]],
        [self.messageLabel.trailingAnchor constraintEqualToAnchor:self.bubbleView.trailingAnchor 
                                                         constant:-[CelraDesignSystem spacingM]],
        [self.messageLabel.bottomAnchor constraintEqualToAnchor:self.bubbleView.bottomAnchor 
                                                       constant:-[CelraDesignSystem spacingS]]
    ]];
    
    // Timestamp constraints
    [NSLayoutConstraint activateConstraints:@[
        [self.timestampLabel.topAnchor constraintEqualToAnchor:self.bubbleView.bottomAnchor 
                                                      constant:[CelraDesignSystem spacingXS]],
        [self.timestampLabel.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor 
                                                         constant:-[CelraDesignSystem spacingS]]
    ]];
}

- (void)configureWithMessage:(ChatMessage *)message character:(AICharacter *)character {
    self.messageLabel.text = message.text;
    
    // Format timestamp
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"HH:mm";
    self.timestampLabel.text = [formatter stringFromDate:message.timestamp];
    
    if (message.isFromUser) {
        // User message - right aligned, blue bubble
        self.bubbleView.backgroundColor = [CelraDesignSystem accentColor];
        self.messageLabel.textColor = [CelraDesignSystem primaryBackgroundColor];
        
        // Hide avatar for user messages
        self.avatarImageView.hidden = YES;
        
        // Update constraints for right alignment
        self.bubbleLeadingConstraint.active = NO;
        self.bubbleTrailingConstraint.active = YES;
        [self.bubbleView.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.contentView.leadingAnchor 
                                                                   constant:[CelraDesignSystem spacingXL]].active = YES;
        
        // Timestamp alignment
        [self.timestampLabel.trailingAnchor constraintEqualToAnchor:self.bubbleView.trailingAnchor].active = YES;
        
    } else {
        // AI message - left aligned, character color bubble
        self.bubbleView.backgroundColor = [CelraDesignSystem cardBackgroundColor];
        self.messageLabel.textColor = [CelraDesignSystem textPrimaryColor];
        
        // Show avatar for AI messages
        self.avatarImageView.hidden = NO;
        self.avatarImageView.backgroundColor = character.doorColor;
        
        // Update constraints for left alignment
        self.bubbleTrailingConstraint.active = NO;
        self.bubbleLeadingConstraint.active = YES;
        [self.bubbleView.trailingAnchor constraintLessThanOrEqualToAnchor:self.contentView.trailingAnchor 
                                                                 constant:-[CelraDesignSystem spacingXL]].active = YES;
        
        // Avatar positioning
        [self.avatarImageView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor 
                                                           constant:[CelraDesignSystem spacingM]].active = YES;
        
        // Timestamp alignment
        [self.timestampLabel.leadingAnchor constraintEqualToAnchor:self.bubbleView.leadingAnchor].active = YES;
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    
    // Reset constraints
    [self.contentView removeConstraints:self.contentView.constraints];
    [self setupConstraints];
}

@end
