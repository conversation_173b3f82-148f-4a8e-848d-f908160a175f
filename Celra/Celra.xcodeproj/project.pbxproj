// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		DE25BD902E40590000DEF00F /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BD8F2E40590000DEF00F /* AppDelegate.m */; };
		DE25BD932E40590000DEF00F /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BD922E40590000DEF00F /* SceneDelegate.m */; };
		DE25BD962E40590000DEF00F /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BD952E40590000DEF00F /* ViewController.m */; };
		DE25BD992E40590000DEF00F /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE25BD972E40590000DEF00F /* Main.storyboard */; };
		DE25BD9B2E40590200DEF00F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DE25BD9A2E40590200DEF00F /* Assets.xcassets */; };
		DE25BD9E2E40590200DEF00F /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE25BD9C2E40590200DEF00F /* LaunchScreen.storyboard */; };
		DE25BDA12E40590200DEF00F /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDA02E40590200DEF00F /* main.m */; };
		DE25BDAB2E40590200DEF00F /* CelraTests.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDAA2E40590200DEF00F /* CelraTests.m */; };
		DE25BDB52E40590200DEF00F /* CelraUITests.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDB42E40590200DEF00F /* CelraUITests.m */; };
		DE25BDB72E40590200DEF00F /* CelraUITestsLaunchTests.m in Sources */ = {isa = PBXBuildFile; fileRef = DE25BDB62E40590200DEF00F /* CelraUITestsLaunchTests.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		DE25BDA72E40590200DEF00F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DE25BD832E40590000DEF00F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DE25BD8A2E40590000DEF00F;
			remoteInfo = Celra;
		};
		DE25BDB12E40590200DEF00F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DE25BD832E40590000DEF00F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DE25BD8A2E40590000DEF00F;
			remoteInfo = Celra;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		DE25BD8B2E40590000DEF00F /* Celra.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Celra.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DE25BD8E2E40590000DEF00F /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		DE25BD8F2E40590000DEF00F /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		DE25BD912E40590000DEF00F /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		DE25BD922E40590000DEF00F /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		DE25BD942E40590000DEF00F /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		DE25BD952E40590000DEF00F /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		DE25BD982E40590000DEF00F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		DE25BD9A2E40590200DEF00F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		DE25BD9D2E40590200DEF00F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		DE25BD9F2E40590200DEF00F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		DE25BDA02E40590200DEF00F /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		DE25BDA62E40590200DEF00F /* CelraTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CelraTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		DE25BDAA2E40590200DEF00F /* CelraTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CelraTests.m; sourceTree = "<group>"; };
		DE25BDB02E40590200DEF00F /* CelraUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CelraUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		DE25BDB42E40590200DEF00F /* CelraUITests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CelraUITests.m; sourceTree = "<group>"; };
		DE25BDB62E40590200DEF00F /* CelraUITestsLaunchTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CelraUITestsLaunchTests.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DE25BD882E40590000DEF00F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDA32E40590200DEF00F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDAD2E40590200DEF00F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		DE25BD822E40590000DEF00F = {
			isa = PBXGroup;
			children = (
				DE25BD8D2E40590000DEF00F /* Celra */,
				DE25BDA92E40590200DEF00F /* CelraTests */,
				DE25BDB32E40590200DEF00F /* CelraUITests */,
				DE25BD8C2E40590000DEF00F /* Products */,
			);
			sourceTree = "<group>";
		};
		DE25BD8C2E40590000DEF00F /* Products */ = {
			isa = PBXGroup;
			children = (
				DE25BD8B2E40590000DEF00F /* Celra.app */,
				DE25BDA62E40590200DEF00F /* CelraTests.xctest */,
				DE25BDB02E40590200DEF00F /* CelraUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DE25BD8D2E40590000DEF00F /* Celra */ = {
			isa = PBXGroup;
			children = (
				DE25BD8E2E40590000DEF00F /* AppDelegate.h */,
				DE25BD8F2E40590000DEF00F /* AppDelegate.m */,
				DE25BD912E40590000DEF00F /* SceneDelegate.h */,
				DE25BD922E40590000DEF00F /* SceneDelegate.m */,
				DE25BD942E40590000DEF00F /* ViewController.h */,
				DE25BD952E40590000DEF00F /* ViewController.m */,
				DE25BD972E40590000DEF00F /* Main.storyboard */,
				DE25BD9A2E40590200DEF00F /* Assets.xcassets */,
				DE25BD9C2E40590200DEF00F /* LaunchScreen.storyboard */,
				DE25BD9F2E40590200DEF00F /* Info.plist */,
				DE25BDA02E40590200DEF00F /* main.m */,
			);
			path = Celra;
			sourceTree = "<group>";
		};
		DE25BDA92E40590200DEF00F /* CelraTests */ = {
			isa = PBXGroup;
			children = (
				DE25BDAA2E40590200DEF00F /* CelraTests.m */,
			);
			path = CelraTests;
			sourceTree = "<group>";
		};
		DE25BDB32E40590200DEF00F /* CelraUITests */ = {
			isa = PBXGroup;
			children = (
				DE25BDB42E40590200DEF00F /* CelraUITests.m */,
				DE25BDB62E40590200DEF00F /* CelraUITestsLaunchTests.m */,
			);
			path = CelraUITests;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DE25BD8A2E40590000DEF00F /* Celra */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE25BDBA2E40590200DEF00F /* Build configuration list for PBXNativeTarget "Celra" */;
			buildPhases = (
				DE25BD872E40590000DEF00F /* Sources */,
				DE25BD882E40590000DEF00F /* Frameworks */,
				DE25BD892E40590000DEF00F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Celra;
			productName = Celra;
			productReference = DE25BD8B2E40590000DEF00F /* Celra.app */;
			productType = "com.apple.product-type.application";
		};
		DE25BDA52E40590200DEF00F /* CelraTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE25BDBD2E40590200DEF00F /* Build configuration list for PBXNativeTarget "CelraTests" */;
			buildPhases = (
				DE25BDA22E40590200DEF00F /* Sources */,
				DE25BDA32E40590200DEF00F /* Frameworks */,
				DE25BDA42E40590200DEF00F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DE25BDA82E40590200DEF00F /* PBXTargetDependency */,
			);
			name = CelraTests;
			productName = CelraTests;
			productReference = DE25BDA62E40590200DEF00F /* CelraTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		DE25BDAF2E40590200DEF00F /* CelraUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE25BDC02E40590200DEF00F /* Build configuration list for PBXNativeTarget "CelraUITests" */;
			buildPhases = (
				DE25BDAC2E40590200DEF00F /* Sources */,
				DE25BDAD2E40590200DEF00F /* Frameworks */,
				DE25BDAE2E40590200DEF00F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DE25BDB22E40590200DEF00F /* PBXTargetDependency */,
			);
			name = CelraUITests;
			productName = CelraUITests;
			productReference = DE25BDB02E40590200DEF00F /* CelraUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DE25BD832E40590000DEF00F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					DE25BD8A2E40590000DEF00F = {
						CreatedOnToolsVersion = 15.0.1;
					};
					DE25BDA52E40590200DEF00F = {
						CreatedOnToolsVersion = 15.0.1;
						TestTargetID = DE25BD8A2E40590000DEF00F;
					};
					DE25BDAF2E40590200DEF00F = {
						CreatedOnToolsVersion = 15.0.1;
						TestTargetID = DE25BD8A2E40590000DEF00F;
					};
				};
			};
			buildConfigurationList = DE25BD862E40590000DEF00F /* Build configuration list for PBXProject "Celra" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DE25BD822E40590000DEF00F;
			productRefGroup = DE25BD8C2E40590000DEF00F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DE25BD8A2E40590000DEF00F /* Celra */,
				DE25BDA52E40590200DEF00F /* CelraTests */,
				DE25BDAF2E40590200DEF00F /* CelraUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DE25BD892E40590000DEF00F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE25BD9E2E40590200DEF00F /* LaunchScreen.storyboard in Resources */,
				DE25BD9B2E40590200DEF00F /* Assets.xcassets in Resources */,
				DE25BD992E40590000DEF00F /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDA42E40590200DEF00F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDAE2E40590200DEF00F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DE25BD872E40590000DEF00F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE25BD962E40590000DEF00F /* ViewController.m in Sources */,
				DE25BD902E40590000DEF00F /* AppDelegate.m in Sources */,
				DE25BDA12E40590200DEF00F /* main.m in Sources */,
				DE25BD932E40590000DEF00F /* SceneDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDA22E40590200DEF00F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE25BDAB2E40590200DEF00F /* CelraTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE25BDAC2E40590200DEF00F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE25BDB72E40590200DEF00F /* CelraUITestsLaunchTests.m in Sources */,
				DE25BDB52E40590200DEF00F /* CelraUITests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		DE25BDA82E40590200DEF00F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DE25BD8A2E40590000DEF00F /* Celra */;
			targetProxy = DE25BDA72E40590200DEF00F /* PBXContainerItemProxy */;
		};
		DE25BDB22E40590200DEF00F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DE25BD8A2E40590000DEF00F /* Celra */;
			targetProxy = DE25BDB12E40590200DEF00F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		DE25BD972E40590000DEF00F /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DE25BD982E40590000DEF00F /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		DE25BD9C2E40590200DEF00F /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DE25BD9D2E40590200DEF00F /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		DE25BDB82E40590200DEF00F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		DE25BDB92E40590200DEF00F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DE25BDBB2E40590200DEF00F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Celra/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.Celra;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DE25BDBC2E40590200DEF00F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Celra/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.Celra;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		DE25BDBE2E40590200DEF00F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.CelraTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Celra.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Celra";
			};
			name = Debug;
		};
		DE25BDBF2E40590200DEF00F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.CelraTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Celra.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Celra";
			};
			name = Release;
		};
		DE25BDC12E40590200DEF00F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.CelraUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Celra;
			};
			name = Debug;
		};
		DE25BDC22E40590200DEF00F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = test.duckegg.ios.CelraUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Celra;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DE25BD862E40590000DEF00F /* Build configuration list for PBXProject "Celra" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE25BDB82E40590200DEF00F /* Debug */,
				DE25BDB92E40590200DEF00F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE25BDBA2E40590200DEF00F /* Build configuration list for PBXNativeTarget "Celra" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE25BDBB2E40590200DEF00F /* Debug */,
				DE25BDBC2E40590200DEF00F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE25BDBD2E40590200DEF00F /* Build configuration list for PBXNativeTarget "CelraTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE25BDBE2E40590200DEF00F /* Debug */,
				DE25BDBF2E40590200DEF00F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE25BDC02E40590200DEF00F /* Build configuration list for PBXNativeTarget "CelraUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE25BDC12E40590200DEF00F /* Debug */,
				DE25BDC22E40590200DEF00F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DE25BD832E40590000DEF00F /* Project object */;
}
